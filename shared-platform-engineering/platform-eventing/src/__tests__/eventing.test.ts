import { connect } from 'nats';
import { v4 as uuidv4 } from 'uuid';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import {
  createEvent,
  createPrismaOutboxStorage,
  EventPublisher,
  EventSubscriber,
  EventTypes,
  OutboxManager,
  PublisherConfigs,
  StreamConfigs,
  SubscriberConfigs,
} from '../index';

// Mock NATS connection
vi.mock('nats', () => ({
  connect: vi.fn(() => ({
    close: vi.fn(),
    closed: Promise.resolve(),
    drain: vi.fn(),
    flush: vi.fn(),
    info: vi.fn(() => ({ jetstream: true })),
    jetstream: vi.fn(() => ({
      consumers: vi.fn(() => ({
        get: vi.fn(),
      })),
      publish: vi.fn(),
      streams: vi.fn(() => ({
        get: vi.fn(),
      })),
      subscribe: vi.fn(() => ({
        ack: vi.fn(),
        closed: Promise.resolve(),
        drain: vi.fn(),
        error: vi.fn(),
        isClosed: vi.fn(() => false),
        nak: vi.fn(),
        next: vi.fn(),
        unsubscribe: vi.fn(),
      })),
    })),
    publish: vi.fn(),
    request: vi.fn(),
    status: vi.fn(() => 'connected'),
    subscribe: vi.fn(() => ({
      closed: Promise.resolve(),
      drain: vi.fn(),
      error: vi.fn(),
      isClosed: vi.fn(() => false),
      next: vi.fn(),
      unsubscribe: vi.fn(),
    })),
  })),
}));

// Mock Prisma Client for OutboxManager tests
const mockPrismaClient = {
  // biome-ignore lint/style/useNamingConvention: Prisma Client uses $transaction
  $transaction: vi.fn((callback) => callback(mockPrismaClient)),
  outbox: {
    create: vi.fn(),
  },
};

describe('EventPublisher', () => {
  let publisher: EventPublisher;

  beforeEach(() => {
    publisher = new EventPublisher(
      PublisherConfigs.appointment('test-publisher'),
    );
    vi.clearAllMocks();
  });

  it('should connect to NATS', async () => {
    await publisher.connect();
    expect(connect).toHaveBeenCalled();
  });

  it('should publish an event', async () => {
    await publisher.connect();
    const mockEvent = createEvent()
      .type(EventTypes.created('test'))
      .aggregate(uuidv4(), 'test')
      .data({ foo: 'bar' })
      .source('test-source')
      .build();

    await publisher.publish(mockEvent);
    const natsConnection = (connect as vi.Mock).mock.results[0].value;
    expect(natsConnection.jetstream().publish).toHaveBeenCalledWith(
      'appointment.events.test.created',
      expect.any(Uint8Array),
      expect.any(Object),
    );
  });

  it('should disconnect from NATS', async () => {
    await publisher.connect();
    await publisher.disconnect();
    const natsConnection = (connect as vi.Mock).mock.results[0].value;
    expect(natsConnection.close).toHaveBeenCalled();
  });
});

describe('EventSubscriber', () => {
  let subscriber: EventSubscriber;

  beforeEach(() => {
    subscriber = new EventSubscriber(
      SubscriberConfigs.appointment('test-subscriber'),
    );
    vi.clearAllMocks();
  });

  it('should connect to NATS', async () => {
    await subscriber.connect();
    expect(connect).toHaveBeenCalled();
  });

  it('should subscribe to a stream and process messages', async () => {
    await subscriber.connect();
    const mockCallback = vi.fn();
    const streamName = StreamConfigs.appointments.name;
    const subject = StreamConfigs.appointments.subjects[0];

    // Simulate a message being received
    const mockMsg = {
      ack: vi.fn(),
      data: new TextEncoder().encode(
        JSON.stringify({ data: { foo: 'bar' }, eventType: 'test.created' }),
      ),
      nak: vi.fn(),
    };

    // Mock the subscribe method to immediately call the callback with a mock message
    const natsConnection = (connect as vi.Mock).mock.results[0].value;
    natsConnection
      .jetstream()
      .subscribe.mockImplementation((_stream, _subject, callback) => {
        callback(null, mockMsg); // Simulate successful message reception
        return {
          ack: vi.fn(),
          closed: Promise.resolve(),
          drain: vi.fn(),
          error: vi.fn(),
          isClosed: vi.fn(() => false),
          nak: vi.fn(),
          // Return a mock subscription object
          next: vi.fn(),
          unsubscribe: vi.fn(),
        };
      });

    subscriber.subscribe(streamName, subject, mockCallback);

    // Wait for the async callback to potentially execute
    await vi.waitFor(() => {
      expect(mockCallback).toHaveBeenCalledWith(null, mockMsg);
      expect(mockMsg.ack).toHaveBeenCalled();
    });
  });

  it('should disconnect from NATS', async () => {
    await subscriber.connect();
    await subscriber.disconnect();
    const natsConnection = (connect as vi.Mock).mock.results[0].value;
    expect(natsConnection.close).toHaveBeenCalled();
  });
});

describe('OutboxManager', () => {
  let outboxManager: OutboxManager;
  let publisher: EventPublisher;

  beforeEach(() => {
    publisher = new EventPublisher(
      PublisherConfigs.appointment('test-outbox-publisher'),
    );
    outboxManager = new OutboxManager(
      createPrismaOutboxStorage(
        mockPrismaClient as PrismaClientWithOutbox,
        'test-source',
      ),
      publisher,
    );
    vi.clearAllMocks();
  });

  it('should store an event in the outbox', async () => {
    const mockEvent = createEvent()
      .type(EventTypes.created('appointment'))
      .aggregate(uuidv4(), 'appointment')
      .data({ foo: 'bar' })
      .source('test-source')
      .build();

    await outboxManager.storeEvent(mockEvent, mockPrismaClient);

    expect(mockPrismaClient.$transaction).toHaveBeenCalled();
    expect(mockPrismaClient.outbox.create).toHaveBeenCalledWith(
      expect.objectContaining({
        data: expect.objectContaining({
          aggregateId: mockEvent.aggregateId,
          eventData: mockEvent.data,
          eventType: mockEvent.eventType,
        }),
      }),
    );
  });
});
