/**
 * 📅 Appointment Event Schemas
 *
 * Defines versioned schemas for appointment-related events with proper
 * validation, compatibility, and evolution support.
 */

import type { JSONSchemaType } from 'ajv';
import {
  type EventSchema,
  globalEventSchemaRegistry,
} from '../EventSchemaRegistry';

// ============================================================================
// Appointment Created Event Types
// ============================================================================

export interface AppointmentCreatedEventDataV1 {
  appointmentId: string;
  customerId: string;
  staffId: string;
  treatmentId: string;
  salonId: string;
  scheduledAt: string; // ISO 8601 datetime
  duration: number; // minutes
  status: 'SCHEDULED';
  notes?: string;
  metadata: {
    source: string;
    createdBy?: string;
    clientVersion?: string;
  };
}

export interface AppointmentCreatedEventDataV2 {
  appointmentId: string;
  customerId: string;
  staffId: string;
  treatmentId: string;
  salonId: string;
  scheduledAt: string; // ISO 8601 datetime
  duration: number; // minutes
  status: 'SCHEDULED';
  notes?: string;
  pricing: {
    basePrice: number;
    currency: string;
    discounts?: Array<{
      type: string;
      amount: number;
      reason?: string;
    }>;
  };
  metadata: {
    source: string;
    createdBy?: string;
    clientVersion?: string;
    bookingChannel?: 'web' | 'mobile' | 'phone' | 'walk-in';
  };
}

// ============================================================================
// JSON Schemas
// ============================================================================

const appointmentCreatedSchemaV1: JSONSchemaType<AppointmentCreatedEventDataV1> =
  {
    additionalProperties: false,
    properties: {
      appointmentId: {
        maxLength: 100,
        minLength: 1,
        pattern: '^[a-zA-Z0-9-_]+$',
        type: 'string',
      },
      customerId: {
        maxLength: 100,
        minLength: 1,
        pattern: '^[a-zA-Z0-9-_]+$',
        type: 'string',
      },
      duration: {
        maximum: 480,
        minimum: 15,
        type: 'number', // 8 hours max
      },
      metadata: {
        additionalProperties: false,
        properties: {
          clientVersion: {
            maxLength: 50,
            nullable: true,
            type: 'string',
          },
          createdBy: {
            maxLength: 100,
            nullable: true,
            type: 'string',
          },
          source: {
            maxLength: 100,
            minLength: 1,
            type: 'string',
          },
        },
        required: ['source'],
        type: 'object',
      },
      notes: {
        maxLength: 1000,
        nullable: true,
        type: 'string',
      },
      salonId: {
        maxLength: 100,
        minLength: 1,
        pattern: '^[a-zA-Z0-9-_]+$',
        type: 'string',
      },
      scheduledAt: {
        format: 'date-time',
        type: 'string',
      },
      staffId: {
        maxLength: 100,
        minLength: 1,
        pattern: '^[a-zA-Z0-9-_]+$',
        type: 'string',
      },
      status: {
        const: 'SCHEDULED',
        type: 'string',
      },
      treatmentId: {
        maxLength: 100,
        minLength: 1,
        pattern: '^[a-zA-Z0-9-_]+$',
        type: 'string',
      },
    },
    required: [
      'appointmentId',
      'customerId',
      'staffId',
      'treatmentId',
      'salonId',
      'scheduledAt',
      'duration',
      'status',
      'metadata',
    ],
    type: 'object',
  };

const appointmentCreatedSchemaV2: JSONSchemaType<AppointmentCreatedEventDataV2> =
  {
    additionalProperties: false,
    properties: {
      appointmentId: {
        maxLength: 100,
        minLength: 1,
        pattern: '^[a-zA-Z0-9-_]+$',
        type: 'string',
      },
      customerId: {
        maxLength: 100,
        minLength: 1,
        pattern: '^[a-zA-Z0-9-_]+$',
        type: 'string',
      },
      duration: {
        maximum: 480,
        minimum: 15,
        type: 'number', // 8 hours max
      },
      metadata: {
        additionalProperties: false,
        properties: {
          bookingChannel: {
            enum: ['web', 'mobile', 'phone', 'walk-in'],
            nullable: true,
            type: 'string',
          },
          clientVersion: {
            maxLength: 50,
            nullable: true,
            type: 'string',
          },
          createdBy: {
            maxLength: 100,
            nullable: true,
            type: 'string',
          },
          source: {
            maxLength: 100,
            minLength: 1,
            type: 'string',
          },
        },
        required: ['source'],
        type: 'object',
      },
      notes: {
        maxLength: 1000,
        nullable: true,
        type: 'string',
      },
      pricing: {
        additionalProperties: false,
        properties: {
          basePrice: {
            minimum: 0,
            type: 'number',
          },
          currency: {
            pattern: '^[A-Z]{3}$',
            type: 'string', // ISO 4217 currency codes
          },
          discounts: {
            items: {
              additionalProperties: false,
              properties: {
                amount: {
                  minimum: 0,
                  type: 'number',
                },
                reason: {
                  maxLength: 200,
                  nullable: true,
                  type: 'string',
                },
                type: {
                  maxLength: 50,
                  minLength: 1,
                  type: 'string',
                },
              },
              required: ['type', 'amount'],
              type: 'object',
            },
            nullable: true,
            type: 'array',
          },
        },
        required: ['basePrice', 'currency'],
        type: 'object',
      },
      salonId: {
        maxLength: 100,
        minLength: 1,
        pattern: '^[a-zA-Z0-9-_]+$',
        type: 'string',
      },
      scheduledAt: {
        format: 'date-time',
        type: 'string',
      },
      staffId: {
        maxLength: 100,
        minLength: 1,
        pattern: '^[a-zA-Z0-9-_]+$',
        type: 'string',
      },
      status: {
        const: 'SCHEDULED',
        type: 'string',
      },
      treatmentId: {
        maxLength: 100,
        minLength: 1,
        pattern: '^[a-zA-Z0-9-_]+$',
        type: 'string',
      },
    },
    required: [
      'appointmentId',
      'customerId',
      'staffId',
      'treatmentId',
      'salonId',
      'scheduledAt',
      'duration',
      'status',
      'pricing',
      'metadata',
    ],
    type: 'object',
  };

// ============================================================================
// Schema Definitions
// ============================================================================

export const appointmentCreatedSchemas: EventSchema[] = [
  {
    compatibilityMode: 'backward',
    description:
      'Initial appointment created event schema with basic appointment information',
    eventType: 'appointment.created',
    examples: [
      {
        appointmentId: 'apt-123',
        customerId: 'cust-456',
        duration: 60,
        metadata: {
          clientVersion: '1.0.0',
          createdBy: 'user-999',
          source: 'appointment-planner-backend',
        },
        notes: 'First appointment for facial treatment',
        salonId: 'salon-202',
        scheduledAt: '2024-01-15T10:00:00Z',
        staffId: 'staff-789',
        status: 'PENDING',
        treatmentId: 'treat-101',
      },
    ],
    schema: appointmentCreatedSchemaV1,
    version: 1,
  },
  {
    compatibilityMode: 'backward',
    description:
      'Enhanced appointment created event schema with pricing information and booking channel',
    eventType: 'appointment.created',
    examples: [
      {
        appointmentId: 'apt-123',
        customerId: 'cust-456',
        duration: 60,
        metadata: {
          bookingChannel: 'web',
          clientVersion: '2.0.0',
          createdBy: 'user-999',
          source: 'appointment-planner-backend',
        },
        notes: 'First appointment for facial treatment',
        pricing: {
          basePrice: 85.0,
          currency: 'USD',
          discounts: [
            {
              amount: 10.0,
              reason: 'Welcome discount',
              type: 'first-time-customer',
            },
          ],
        },
        salonId: 'salon-202',
        scheduledAt: '2024-01-15T10:00:00Z',
        staffId: 'staff-789',
        status: 'SCHEDULED',
        treatmentId: 'treat-101',
      },
    ],
    schema: appointmentCreatedSchemaV2,
    version: 2,
  },
];

// ============================================================================
// Schema Registration
// ============================================================================

export function registerAppointmentSchemas(): void {
  for (const schema of appointmentCreatedSchemas) {
    globalEventSchemaRegistry.registerSchema(schema);
  }
}

// Auto-register schemas when module is imported
registerAppointmentSchemas();
