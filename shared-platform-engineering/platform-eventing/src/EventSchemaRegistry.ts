/**
 * 📋 Event Schema Registry
 *
 * Provides event schema versioning, validation, and compatibility checking
 * for reliable event-driven architecture with backward/forward compatibility.
 */

import Ajv from 'ajv';
import addFormats from 'ajv-formats';

// ============================================================================
// Core Types
// ============================================================================

export interface EventSchema {
  eventType: string;
  version: number;
  schema: Record<string, unknown>;
  compatibilityMode: CompatibilityMode;
  description?: string;
  examples?: unknown[];
  deprecated?: boolean;
  deprecationMessage?: string;
}

export type CompatibilityMode =
  | 'backward' // New schema can read old data
  | 'forward' // Old schema can read new data
  | 'full' // Both backward and forward compatible
  | 'none'; // No compatibility guarantees

export interface SchemaValidationResult {
  valid: boolean;
  errors?: string[];
  warnings?: string[];
  version: number;
}

export interface SchemaCompatibilityResult {
  compatible: boolean;
  issues: string[];
  breakingChanges: string[];
  warnings: string[];
}

// ============================================================================
// Event Schema Registry
// ============================================================================

export class EventSchemaRegistry {
  private schemas = new Map<string, Map<number, EventSchema>>();
  private ajv: Ajv;

  constructor() {
    this.ajv = new Ajv({
      allErrors: true,
      removeAdditional: false,
      strict: false,
      verbose: true,
    });
    addFormats(this.ajv);
  }

  /**
   * Register a new event schema version
   */
  registerSchema(schema: EventSchema): void {
    const { eventType, version } = schema;

    if (!this.schemas.has(eventType)) {
      this.schemas.set(eventType, new Map());
    }

    const eventSchemas = this.schemas.get(eventType);
    // The check above ensures eventSchemas is not undefined here

    // Check compatibility with existing versions
    if (eventSchemas && eventSchemas.size > 0) {
      const latestVersion = Math.max(...eventSchemas.keys());
      const latestSchema = eventSchemas.get(latestVersion);
      if (!latestSchema) {
        // This should not happen if size > 0 and latestVersion is from keys()
        throw new Error(
          `Latest schema not found for type ${eventType} v${latestVersion}.`,
        );
      }

      const compatibility = this.checkCompatibility(latestSchema, schema);
      if (!compatibility.compatible) {
        throw new Error(
          `Schema compatibility check failed for ${eventType} v${version}: ${compatibility.issues.join(
            ', ',
          )}`,
        );
      }
    }

    eventSchemas?.set(version, schema);
  }

  /**
   * Get schema for specific event type and version
   */
  getSchema(eventType: string, version?: number): EventSchema | null {
    const eventSchemas = this.schemas.get(eventType);
    if (!eventSchemas) return null;

    if (version !== undefined) {
      return eventSchemas.get(version) || null;
    }

    // Return latest version
    const latestVersion = Math.max(...eventSchemas.keys());
    return eventSchemas.get(latestVersion) || null;
  }

  /**
   * Get all versions for an event type
   */
  getSchemaVersions(eventType: string): number[] {
    const eventSchemas = this.schemas.get(eventType);
    return eventSchemas ? Array.from(eventSchemas.keys()).sort() : [];
  }

  /**
   * Validate event data against schema
   */
  validateEvent(
    eventType: string,
    eventData: unknown,
    version?: number,
  ): SchemaValidationResult {
    const schema = this.getSchema(eventType, version);

    if (!schema) {
      return {
        errors: [
          `No schema found for event type: ${eventType}${
            version ? ` v${version}` : ''
          }`,
        ],
        valid: false,
        version: version || 0,
      };
    }

    const validate = this.ajv.compile(schema.schema);
    const valid = validate(eventData);

    const result: SchemaValidationResult = {
      valid,
      version: schema.version,
    };

    if (!valid && validate.errors) {
      result.errors = validate.errors.map(
        (error) => `${error.instancePath || 'root'}: ${error.message}`,
      );
    }

    // Add deprecation warnings
    if (schema.deprecated) {
      result.warnings = [
        `Schema ${eventType} v${schema.version} is deprecated${
          schema.deprecationMessage ? `: ${schema.deprecationMessage}` : ''
        }`,
      ];
    }

    return result;
  }

  /**
   * Check compatibility between two schema versions
   */
  checkCompatibility(
    oldSchema: EventSchema,
    newSchema: EventSchema,
  ): SchemaCompatibilityResult {
    const result: SchemaCompatibilityResult = {
      breakingChanges: [],
      compatible: true,
      issues: [],
      warnings: [],
    };

    // Version must be incremental
    if (newSchema.version <= oldSchema.version) {
      result.compatible = false;
      result.issues.push(
        `New version ${newSchema.version} must be greater than ${oldSchema.version}`,
      );
    }

    // Check compatibility mode requirements
    switch (newSchema.compatibilityMode) {
      case 'backward':
        this.checkBackwardCompatibility(oldSchema, newSchema, result);
        break;
      case 'forward':
        this.checkForwardCompatibility(oldSchema, newSchema, result);
        break;
      case 'full':
        this.checkBackwardCompatibility(oldSchema, newSchema, result);
        this.checkForwardCompatibility(oldSchema, newSchema, result);
        break;
      case 'none':
        result.warnings.push(
          'No compatibility guarantees - breaking changes allowed',
        );
        break;
    }

    return result;
  }

  /**
   * Get all registered event types
   */
  getEventTypes(): string[] {
    return Array.from(this.schemas.keys());
  }

  /**
   * Check if event type exists
   */
  hasEventType(eventType: string): boolean {
    return this.schemas.has(eventType);
  }

  /**
   * Remove deprecated schemas older than specified version
   */
  cleanupDeprecatedSchemas(
    eventType: string,
    keepVersionsAfter: number,
  ): number {
    const eventSchemas = this.schemas.get(eventType);
    if (!eventSchemas) return 0;

    let removedCount = 0;
    for (const [version, schema] of eventSchemas.entries()) {
      if (version <= keepVersionsAfter && schema.deprecated) {
        eventSchemas.delete(version);
        removedCount++;
      }
    }

    return removedCount;
  }

  // ============================================================================
  // Private Methods
  // ============================================================================

  private checkBackwardCompatibility(
    oldSchema: EventSchema,
    newSchema: EventSchema,
    result: SchemaCompatibilityResult,
  ): void {
    // Simplified compatibility check - in production, use a proper JSON Schema diff library
    const oldProps =
      (oldSchema.schema.properties as Record<string, unknown>) || {};
    const newProps =
      (newSchema.schema.properties as Record<string, unknown>) || {};
    const oldRequired = (oldSchema.schema.required as string[]) || [];
    const newRequired = (newSchema.schema.required as string[]) || [];

    // Check for removed required fields
    for (const field of oldRequired) {
      if (!newRequired.includes(field)) {
        result.compatible = false;
        result.breakingChanges.push(`Required field '${field}' was removed`);
      }
    }

    // Check for removed fields
    for (const field of Object.keys(oldProps)) {
      if (!(field in newProps)) {
        result.compatible = false;
        result.breakingChanges.push(`Field '${field}' was removed`);
      }
    }

    // Check for type changes
    for (const field of Object.keys(oldProps)) {
      if (field in newProps) {
        const oldType = (oldProps[field] as Record<string, unknown>)?.type;
        const newType = (newProps[field] as Record<string, unknown>)?.type;
        if (oldType && newType && oldType !== newType) {
          result.compatible = false;
          result.breakingChanges.push(
            `Field '${field}' type changed from ${oldType} to ${newType}`,
          );
        }
      }
    }
  }

  private checkForwardCompatibility(
    oldSchema: EventSchema,
    newSchema: EventSchema,
    result: SchemaCompatibilityResult,
  ): void {
    const newRequired = (newSchema.schema.required as string[]) || [];
    const oldRequired = (oldSchema.schema.required as string[]) || [];

    // Check for new required fields
    for (const field of newRequired) {
      if (!oldRequired.includes(field)) {
        result.compatible = false;
        result.breakingChanges.push(`New required field '${field}' added`);
      }
    }
  }
}

// ============================================================================
// Global Registry Instance
// ============================================================================

export const globalEventSchemaRegistry = new EventSchemaRegistry();
