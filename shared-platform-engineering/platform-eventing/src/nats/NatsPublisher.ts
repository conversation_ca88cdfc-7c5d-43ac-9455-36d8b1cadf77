import {
  connect,
  headers,
  type JetStreamClient,
  type NatsConnection,
  StringCodec,
} from 'nats';
import { EventPublisher } from '../EventPublisher';
import type { DomainEvent } from '../types';

export interface NatsPublisherOptions {
  natsUrl: string;
  streamName: string;
  serviceName: string;
  subjects: string[];
}

export class NatsPublisher extends EventPublisher {
  constructor(private opts: NatsPublisherOptions) {
    super({
      natsUrl: opts.natsUrl,
      serviceName: opts.serviceName,
      stream: {
        name: opts.streamName,
        subjects: opts.subjects,
      },
    });
  }

  override async publish(event: DomainEvent): Promise<void> {
    if (!this.isConnected()) {
      throw new Error('NATS connection not established');
    }

    const js = this.jetStream;
    if (!js) {
      throw new Error('JetStream not initialized');
    }

    const subject = `${this.opts.streamName}.${event.eventType}`;
    const data = StringCodec().encode(JSON.stringify(event));
    await js.publish(subject, data, {
      msgID: event.eventId,
    });
  }

  override isConnected(): boolean {
    return super.isConnected();
  }
}

export function createNatsPublisher(
  options: NatsPublisherOptions,
): NatsPublisher {
  return new NatsPublisher(options);
}
