/**
 * 🌟 Beautiful Event Publisher
 *
 * Elegant event publishing with NATS JetStream for Beauty CRM
 */

import {
  connect,
  headers,
  type JetStreamClient,
  type JetStreamManager,
  type NatsConnection,
  RetentionPolicy,
  StorageType,
  StringCodec,
} from 'nats';
import type {
  ConnectionStatus,
  DomainEvent,
  HealthCheckResult,
  PublisherConfig,
} from './types';

/**
 * 🌟 Beautiful Event Publisher
 *
 * Elegant event publishing with NATS JetStream
 */
export class EventPublisher {
  protected connection: NatsConnection | null = null;
  protected jetStream: JetStreamClient | null = null;
  protected jetStreamManager: JetStreamManager | null = null;
  protected status: ConnectionStatus = 'disconnected';
  private codec = StringCodec();

  constructor(private config: PublisherConfig) {}

  /**
   * 🔗 Connect to NATS JetStream
   */
  async connect(): Promise<void> {
    try {
      this.status = 'connecting';

      this.connection = await connect({
        maxPingOut: 3,
        maxReconnectAttempts: this.config.connection?.maxReconnectAttempts || 5,
        name: this.config.serviceName,
        pedantic: false,
        pingInterval: 30000,
        reconnect: true,
        reconnectTimeWait: this.config.connection?.reconnectTimeWait || 2000,
        servers: [this.config.natsUrl || 'nats://localhost:4222'],
        timeout: this.config.connection?.timeout || 15000,
        verbose: false,
        waitOnFirstConnect: true,
      });

      this.jetStream = this.connection.jetstream();
      this.jetStreamManager = await this.connection.jetstreamManager();

      // Create or update stream if configured
      if (this.config.stream) {
        await this.ensureStream();
      }

      this.status = 'connected';
      console.log(
        `✅ EventPublisher connected to NATS for ${this.config.serviceName}`,
      );
    } catch (error) {
      this.status = 'error';
      console.error('❌ Failed to connect EventPublisher:', error);
      throw error;
    }
  }

  /**
   * 🔌 Disconnect from NATS
   */
  async disconnect(): Promise<void> {
    if (this.connection) {
      await this.connection.close();
      this.connection = null;
      this.jetStream = null;
      this.jetStreamManager = null;
      this.status = 'disconnected';
      console.log('🔌 EventPublisher disconnected');
    }
  }

  /**
   * 📤 Publish a domain event
   */
  async publish(event: DomainEvent): Promise<void> {
    if (!this.jetStream) {
      throw new Error('🚫 Not connected to NATS. Call connect() first!');
    }

    try {
      const subject = this.buildSubject(event);
      const data = this.codec.encode(JSON.stringify(event));

      // Create proper NATS headers
      const msgHeaders = headers();
      msgHeaders.set('aggregate-id', event.aggregateId);
      msgHeaders.set('aggregate-type', event.aggregateType);
      msgHeaders.set('event-id', event.eventId);
      msgHeaders.set('event-type', event.eventType);
      msgHeaders.set('source', event.source);

      // Add any default headers from config
      if (this.config.publishing?.defaultHeaders) {
        for (const [key, value] of Object.entries(
          this.config.publishing.defaultHeaders,
        )) {
          msgHeaders.set(key, value);
        }
      }

      const publishOptions = {
        headers: msgHeaders,
        msgID: event.eventId, // For deduplication
      };

      if (this.config.publishing?.waitForAck) {
        const ack = await this.jetStream.publish(subject, data, publishOptions);
        console.log(
          `✅ Published ${event.eventType} event with ack: ${ack.seq}`,
        );
      } else {
        this.jetStream.publish(subject, data, publishOptions);
        console.log(`📤 Published ${event.eventType} event to ${subject}`);
      }
    } catch (error) {
      console.error(`❌ Failed to publish ${event.eventType} event:`, error);
      throw error;
    }
  }

  /**
   * 🏗️ Ensure stream exists
   */
  private async ensureStream(): Promise<void> {
    if (!this.jetStreamManager || !this.config.stream) return;

    try {
      // Map string values to NATS enum values
      const retentionPolicy =
        this.config.stream.retention === 'interest'
          ? RetentionPolicy.Interest
          : this.config.stream.retention === 'workqueue'
            ? RetentionPolicy.Workqueue
            : RetentionPolicy.Limits;

      const storageType =
        this.config.stream.storage === 'memory'
          ? StorageType.Memory
          : StorageType.File;

      const streamConfig = {
        max_age:
          this.config.stream.maxAge || 7 * 24 * 60 * 60 * 1000 * 1000 * 1000,
        max_msgs: this.config.stream.maxMessages || 10000,
        name: this.config.stream.name,
        replicas: this.config.stream.replicas || 1,
        retention: retentionPolicy, // 7 days
        storage: storageType,
        subjects: this.config.stream.subjects,
      };

      await this.jetStreamManager.streams.add(streamConfig);
      console.log(`✅ Stream ${this.config.stream.name} ensured`);
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      if (errorMessage.includes('stream name already in use')) {
        console.log(`ℹ️ Stream ${this.config.stream.name} already exists`);
      } else {
        console.error(
          `❌ Failed to ensure stream ${this.config.stream.name}:`,
          error,
        );
        throw error;
      }
    }
  }

  /**
   * 🏷️ Build subject for event
   */
  private buildSubject(event: DomainEvent): string {
    // Use aggregate type and event type to build subject
    // Format: {aggregateType}.events.{eventType}
    const eventTypeLower = event.eventType
      .toLowerCase()
      .replace(/([A-Z])/g, '-$1')
      .replace(/^-/, '');
    return `${event.aggregateType}.events.${eventTypeLower}`;
  }

  /**
   * 🏥 Health check
   */
  async healthCheck(): Promise<HealthCheckResult> {
    if (!this.connection) {
      return {
        details: {
          error: 'Not connected to NATS',
        },
        healthy: false,
        status: this.status,
      };
    }

    try {
      // Basic connectivity check
      return {
        details: {
          lastSuccess: new Date().toISOString(),
        },
        healthy: this.status === 'connected',
        status: this.status,
      };
    } catch (error) {
      return {
        details: {
          error: error instanceof Error ? error.message : 'Unknown error',
        },
        healthy: false,
        status: this.status,
      };
    }
  }

  /**
   * 📊 Get current connection status
   */
  getStatus(): ConnectionStatus {
    return this.status;
  }

  /**
   * 🔍 Check if connected
   */
  isConnected(): boolean {
    return this.status === 'connected' && this.connection !== null;
  }
}

// ============================================================================
// Factory Functions
// ============================================================================

/**
 * 🏭 Create a new EventPublisher with configuration
 */
export function createPublisher(config: {
  natsUrl?: string;
  serviceName: string;
  stream?: {
    name: string;
    subjects: string[];
    retention?: 'limits' | 'interest' | 'workqueue';
    storage?: 'file' | 'memory';
    maxAge?: number;
    maxMessages?: number;
    replicas?: number;
  };
}): EventPublisher {
  return new EventPublisher({
    natsUrl: config.natsUrl || 'nats://localhost:4222',
    serviceName: config.serviceName,
    stream: config.stream || {
      name: 'DEFAULT_EVENTS',
      subjects: ['events.*'],
    },
  });
}
