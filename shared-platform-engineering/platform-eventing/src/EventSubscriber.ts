/**
 * 📡 EventSubscriber
 *
 * Elegant, type-safe event subscription with NATS JetStream
 */

import {
  connect,
  type JetStreamClient,
  JSONCodec,
  type NatsConnection,
  type Subscription,
  type Msg,
  AckPolicy,
  type JetStreamSubscription, // Import JetStreamSubscription
  type JsMsg, // Import JsMsg
  type ConsumerOpts, // Import ConsumerOpts
  type ConsumerOptsBuilder, // Import ConsumerOptsBuilder
  type ConsumerConfig, // Import ConsumerConfig
  DeliverPolicy, // Import DeliverPolicy
} from 'nats';
import type {
  ConnectionStatus,
  DomainEvent,
  EventHandler,
  HealthCheckResult,
  SubscriberConfig,
  SubscriptionOptions, // Keep custom SubscriptionOptions for now
} from './types';

/**
 * 📡 Beautiful Event Subscriber
 *
 * Elegant subscription to domain events with NATS JetStream
 */
export class EventSubscriber {
  private connection: NatsConnection | null = null;
  private jetStream: JetStreamClient | null = null;
  private readonly jsonCodec = JSONCodec();
  private readonly config: Required<Omit<SubscriberConfig, 'natsUrl'>> & {
    natsUrl?: string;
  };
  private status: ConnectionStatus = 'disconnected';
  private readonly subscriptions = new Map<
    string,
    Subscription | JetStreamSubscription
  >(); // Allow JetStreamSubscription
  private readonly handlers = new Map<string, EventHandler>();
  private errorHandler?: (error: Error) => Promise<void>;

  constructor(config: SubscriberConfig) {
    this.config = {
      connection: {
        maxReconnectAttempts: 10,
        reconnectTimeWait: 2000,
        ...config.connection,
      },
      consumer: {
        ackPolicy: 'explicit',
        ackWait: 30000,
        deliverPolicy: 'new',
        maxDeliver: 3,
        ...config.consumer,
      },
      natsUrl: config.natsUrl,
      servers: config.servers || [config.natsUrl || 'nats://localhost:4222'],
      serviceName: config.serviceName,
    };
  }

  /**
   * 🔌 Connect to NATS
   */
  async connect(): Promise<void> {
    try {
      this.status = 'connecting';

      this.connection = await connect({
        maxPingOut: 3,
        maxReconnectAttempts: this.config.connection.maxReconnectAttempts,
        pedantic: false,
        pingInterval: 30000,
        reconnect: true,
        reconnectTimeWait: this.config.connection.reconnectTimeWait,
        servers: this.config.servers,
        timeout: 15000,
        verbose: false,
        waitOnFirstConnect: true,
      });

      this.jetStream = this.connection.jetstream();
      this.status = 'connected';
      console.log(
        `🎯 EventSubscriber connected to NATS (${this.config.serviceName})`,
      );
    } catch (error) {
      this.status = 'error';
      console.error('💥 Failed to connect to NATS:', error);
      throw error;
    }
  }

  /**
   * 📨 Subscribe to regular NATS subject
   */
  async subscribe<T extends DomainEvent>(
    subject: string,
    handler: EventHandler<T>,
  ): Promise<void> {
    if (!this.connection) {
      throw new Error('🚫 Not connected to NATS. Call connect() first!');
    }

    try {
      const subscription = this.connection.subscribe(subject);
      this.subscriptions.set(subject, subscription);
      this.handlers.set(subject, handler as EventHandler);
      this.processMessages(subject, subscription);
      console.log(`📡 Subscribed to ${subject}`);
    } catch (error) {
      console.error(`💥 Failed to subscribe to ${subject}:`, error);
      throw error;
    }
  }

  /**
   * 🌊 Subscribe to JetStream
   */
  async subscribeToStream<T extends DomainEvent>(
    streamName: string,
    subject: string,
    handler: EventHandler<T>,
    options?: SubscriptionOptions,
  ): Promise<void> {
    if (!this.connection) {
      throw new Error('🚫 Not connected to NATS. Call connect() first!');
    }

    try {
      const jsm = await this.connection.jetstreamManager();

      const consumerConfig: Partial<ConsumerConfig> = {
        durable_name:
          options?.consumerName || `${this.config.serviceName}-${streamName}`,
        ack_policy: options?.ackPolicy
          ? this.mapAckPolicy(options.ackPolicy)
          : AckPolicy.Explicit,
        deliver_policy: options?.deliverPolicy
          ? this.mapDeliverPolicy(options.deliverPolicy)
          : undefined,
        // Add other relevant options from SubscriptionOptions to ConsumerConfig
      };

      // Ensure the consumer exists (add or update)
      await jsm.consumers.add(streamName, consumerConfig);

      // Get the consumer and subscribe
      // const consumer = await this.jetStream!.consumers.get(
      //   streamName,
      //   consumerConfig.durable_name,
      // );

      // Use the consume method for push-like delivery
      // The consume method takes options as the first argument and the callback as the second.
      // const subscription = consumer.consume(
      //   {
      //     /* ConsumeOptions if needed */
      //   }, // Placeholder for options
      //   (msg: JsMsg) => {
      //     try {
      //       const event = this.jsonCodec.decode(msg.data) as T; // Cast to T
      //       handler(event);
      //       msg.ack();
      //       console.log(
      //         `✅ Processed and acknowledged ${event.eventType} event from ${key}`,
      //       );
      //     } catch (error) {
      //       console.error(`💥 Failed to process message from ${key}:`, error);
      //       msg.nak();
      //       if (this.errorHandler) {
      //         this.errorHandler(error as Error);
      //       }
      //     }
      //   },
      // );

      const key = `${streamName}:${subject}`;
      // consumer.consume() does not return a standard Subscription or JetStreamSubscription
      // We might need a different way to track these or adjust the subscriptions map type.
      // For now, we won't add it to the map to resolve the type error.
      // this.subscriptions.set(key, subscription);
      this.handlers.set(key, handler as EventHandler);

      console.log(`🌊 Subscribed to stream ${streamName} subject ${subject}`);
    } catch (error) {
      console.error(
        `💥 Failed to subscribe to stream ${streamName} subject ${subject}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * 🚨 Set error handler
   */
  onError(handler: (error: Error) => Promise<void>): void {
    this.errorHandler = handler;
  }

  /**
   * 🔄 Process regular NATS messages
   */
  private async processMessages(
    subject: string,
    subscription: Subscription,
  ): Promise<void> {
    const handler = this.handlers.get(subject);
    if (!handler) return;

    (async () => {
      for await (const msg of subscription) {
        try {
          const event = this.jsonCodec.decode(msg.data) as DomainEvent;
          await handler(event);
          console.log(`✅ Processed ${event.eventType} event from ${subject}`);
        } catch (error) {
          console.error(`💥 Failed to process message from ${subject}:`, error);
          if (this.errorHandler) {
            await this.errorHandler(error as Error);
          }
        }
      }
    })().catch(async (err) => {
      console.error(`💥 Subscription error for ${subject}:`, err);
      if (this.errorHandler) {
        await this.errorHandler(err as Error);
      }
    });
  }

  /**
   * 🔌 Disconnect from NATS
   */
  async disconnect(): Promise<void> {
    if (!this.connection) return;

    const closePromise = this.connection.closed();

    for (const [key, subscription] of this.subscriptions) {
      try {
        subscription.unsubscribe();
        console.log(`🔇 Unsubscribed from ${key}`);
      } catch (error) {
        console.error(`💥 Failed to unsubscribe from ${key}:`, error);
      }
    }

    await this.connection.close();
    const err = await closePromise;

    if (err) {
      console.error('💥 Connection closed with error:', err);
    } else {
      console.log('👋 Disconnected from NATS');
    }
  }

  /**
   * 🏥 Health check
   */
  async healthCheck(): Promise<HealthCheckResult> {
    if (!this.connection || !this.connection.isClosed()) {
      return {
        details: {
          error: 'Not connected to NATS',
        },
        healthy: false,
        status: this.status,
      };
    }

    try {
      return {
        details: {
          lastSuccess: new Date().toISOString(),
          subscriptions: Array.from(this.subscriptions.keys()),
        },
        healthy: this.status === 'connected',
        status: this.status,
      };
    } catch (error) {
      return {
        details: {
          error: (error as Error).message,
        },
        healthy: false,
        status: this.status,
      };
    }
  }

  /**
   * 📊 Get connection status
   */
  getStatus(): ConnectionStatus {
    return this.status;
  }

  /**
   * 🔍 Check if connected
   */
  isConnected(): boolean {
    return (
      this.status === 'connected' &&
      this.connection !== null &&
      !this.connection.isClosed()
    );
  }

  /**
   * 📋 Get active subscriptions
   */
  getSubscriptions(): string[] {
    return Array.from(this.subscriptions.keys());
  }

  /**
   * Maps local AckPolicy string literal to NATS AckPolicy enum.
   */
  private mapAckPolicy(ackPolicy: 'none' | 'all' | 'explicit'): AckPolicy {
    switch (ackPolicy) {
      case 'none':
        return AckPolicy.None;
      case 'all':
        return AckPolicy.All;
      case 'explicit':
        return AckPolicy.Explicit;
      default:
        // This case should ideally not be reached if the type is correct,
        // but as a fallback, return Explicit.
        return AckPolicy.Explicit;
    }
  }

  /**
   * Maps local DeliverPolicy string literal to NATS DeliverPolicy type.
   */
  private mapDeliverPolicy(
    deliverPolicy:
      | 'all'
      | 'last'
      | 'new'
      | 'by_start_sequence'
      | 'by_start_time',
  ): DeliverPolicy {
    switch (deliverPolicy) {
      case 'all':
        return DeliverPolicy.All;
      case 'last':
        return DeliverPolicy.Last;
      case 'new':
        return DeliverPolicy.New;
      case 'by_start_sequence':
        return DeliverPolicy.StartSequence;
      case 'by_start_time':
        return DeliverPolicy.StartTime;
      default:
        // Fallback, though ideally covered by type
        return DeliverPolicy.New;
    }
  }
}

// ============================================================================
// Factory Functions
// ============================================================================

/**
 * 🏭 Create a new EventSubscriber with configuration
 */
export function createSubscriber(config: {
  natsUrl?: string;
  serviceName: string;
  consumer?: {
    name: string;
    deliverPolicy?: 'new' | 'all' | 'last';
    ackPolicy?: 'explicit' | 'none' | 'all';
  };
}): EventSubscriber {
  return new EventSubscriber({
    consumer: config.consumer,
    natsUrl: config.natsUrl || 'nats://localhost:4222',
    serviceName: config.serviceName,
  });
}
