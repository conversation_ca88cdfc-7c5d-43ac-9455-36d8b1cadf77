import { randomUUID } from 'node:crypto';
import type { Prisma, PrismaClient } from '@prisma/client';
import type { AppointmentEvent } from '@beauty-crm/product-appointment-types';

/**
 * Interface for transactional outbox operations
 */
export interface ITransactionalOutbox {
  /**
   * Adds an event to the outbox within a transaction
   */
  add(event: AppointmentEvent, tx: Prisma.TransactionClient): Promise<void>;

  /**
   * Processes pending events from the outbox
   */
  processPendingEvents(
    processFn: (event: AppointmentEvent) => Promise<void>,
    batchSize?: number,
    maxRetries?: number,
  ): Promise<{ processed: number; failed: number }>;
}

/**
 * Default implementation of the transactional outbox using Prisma
 */
export class PrismaTransactionalOutbox implements ITransactionalOutbox {
  private readonly prisma: PrismaClient;
  private readonly outboxTable: string;

  constructor(prisma: PrismaClient, outboxTable = 'outbox_events') {
    this.prisma = prisma;
    this.outboxTable = outboxTable;
  }

  async add(
    event: AppointmentEvent,
    tx: Prisma.TransactionClient,
  ): Promise<void> {
    const client = tx || this.prisma;

    await client.$executeRawUnsafe(
      `INSERT INTO ${this.outboxTable} (id, aggregate_id, event_type, payload, created_at, retry_count, status) 
       VALUES ($1, $2, $3, $4, $5, $6, $7)`,
      randomUUID(),
      event.aggregateId,
      event.eventType,
      JSON.stringify(event),
      new Date(),
      0,
      'PENDING',
    );
  }

  async processPendingEvents(
    processFn: (event: AppointmentEvent) => Promise<void>,
    batchSize = 50,
    maxRetries = 3,
  ): Promise<{ processed: number; failed: number }> {
    let processed = 0;
    let failed = 0;

    // Use a transaction to ensure we don't process the same events multiple times
    await this.prisma.$transaction(async (tx) => {
      // Get batch of pending events, ordered by creation time
      const events = await tx.$queryRawUnsafe<
        Array<{
          id: string;
          payload: string;
          retryCount: number;
        }>
      >(
        `SELECT id, payload, retry_count 
         FROM ${this.outboxTable} 
         WHERE status = 'PENDING' AND retry_count <= $1 
         ORDER BY created_at ASC 
         LIMIT $2 
         FOR UPDATE SKIP LOCKED`,
        maxRetries,
        batchSize,
      );

      if (events.length === 0) {
        return;
      }

      // Process each event
      for (const event of events) {
        try {
          const parsedEvent = JSON.parse(event.payload) as AppointmentEvent;
          await processFn(parsedEvent);

          // Mark as processed
          await tx.$executeRawUnsafe(
            `UPDATE ${this.outboxTable} 
             SET status = 'PROCESSED', processed_at = $1 
             WHERE id = $2`,
            new Date(),
            event.id,
          );
          processed++;
        } catch (error) {
          console.error(`Failed to process outbox event ${event.id}:`, error);

          // Update retry count and last error
          await tx.$executeRawUnsafe(
            `UPDATE ${this.outboxTable} 
             SET retry_count = $1, 
                 last_error = $2, 
                 status = CASE WHEN retry_count >= $3 THEN 'FAILED' ELSE 'PENDING' END,
                 updated_at = $4
             WHERE id = $5`,
            event.retryCount + 1,
            error instanceof Error ? error.message : String(error),
            maxRetries,
            new Date(),
            event.id,
          );

          failed++;
        }
      }
    });

    return { failed, processed };
  }
}
