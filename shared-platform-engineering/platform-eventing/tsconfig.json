{"compilerOptions": {"composite": true, "declaration": true, "declarationMap": true, "emitDecoratorMetadata": true, "esModuleInterop": true, "experimentalDecorators": true, "forceConsistentCasingInFileNames": true, "lib": ["ES2022", "DOM"], "module": "CommonJS", "moduleResolution": "node", "outDir": "./dist", "rootDir": "./src", "skipLibCheck": true, "sourceMap": true, "strict": true, "target": "ES2022"}, "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"], "include": ["src/**/*"], "references": [{"path": "../../shared-product-engineering/product-appointment-types"}]}