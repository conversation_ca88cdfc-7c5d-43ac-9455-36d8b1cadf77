{"dependencies": {"@beauty-crm/platform-environment-names": "workspace:*", "@beauty-crm/platform-logger": "workspace:*", "@beauty-crm/product-responses": "workspace:*", "@hono/node-server": "^1.14.3", "hono": "^4.7.10", "ioredis": "^5.6.1"}, "description": "Platform runtime environment management, configuration, and execution context handling", "devDependencies": {"@types/node": "^22.15.21", "rimraf": "^6.0.1", "typescript": "^5.8.3"}, "main": "dist/index.js", "name": "@beauty-crm/platform-computing-runtime", "private": true, "scripts": {"build": "tsc", "clean": "<PERSON><PERSON><PERSON> dist", "dev": "tsc --watch", "format": "biome format --write --fix .", "lint": "biome lint .", "lint:check": "biome check .", "lint:fix": "biome lint --write .", "test": "echo 'No tests specified for platform-computing-runtime' && exit 0"}, "types": "dist/index.d.ts", "version": "1.0.0"}