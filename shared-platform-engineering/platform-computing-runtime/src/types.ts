import type { ApplicationEnvironment } from '@beauty-crm/platform-environment-names';
import type { Context, Env } from 'hono';

// Define the environment type used by the Hono app
export interface AppEnv extends Env, ComputingConfig {
  // Explicitly define Bindings to satisfy hono's Env type
  // and ensure it holds the ComputingConfig
  Bindings: ComputingConfig;
}

// Computing instance types
export type ComputingInstanceType = 'hono';

export interface IRouter {
  get(
    path: string,
    handler: (c: Context) => Promise<Response> | Response,
  ): void;
  post(
    path: string,
    handler: (c: Context) => Promise<Response> | Response,
  ): void;
  put(
    path: string,
    handler: (c: Context) => Promise<Response> | Response,
  ): void;
  delete(
    path: string,
    handler: (c: Context) => Promise<Response> | Response,
  ): void;
  patch(
    path: string,
    handler: (c: Context) => Promise<Response> | Response,
  ): void;
  getApp(): unknown;
}

export interface BaseComputingInstance {
  readonly type: ComputingInstanceType;
  start(): Promise<void>;
  stop(): Promise<void>;
  getRouter(): IRouter;
}

// KVNamespace interface matching Redis implementation
export interface IKVNamespace {
  get(key: string): Promise<string | null>;
  set(key: string, value: string, ttlSeconds?: number): Promise<void>;
  delete(key: string): Promise<void>;
  disconnect(): Promise<void>;
  getTenantId(): string;
}

// Generic database interface that can be implemented for different providers
export interface Database {
  type:
    | 'postgres'
    | 'neon-tech-postgres'
    | 'aws_rds'
    | 'aws_aurora'
    | 'standalone';
  config: Record<string, unknown>;
}

// Generic key-value store interface
export interface KeyValueStore extends IKVNamespace {
  type: 'redis';
}

// Generic queue interface
export interface Queue {
  type: 'aws_sqs' | 'rabbitmq' | 'red-panda' | 'kafka';
  config: Record<string, unknown>;
}

// Computing instance configuration
export interface ComputingConfig {
  type: ComputingInstanceType;
  env: ApplicationEnvironment;
  database?: Database;
  kvStore?: KeyValueStore;
  queue?: Queue;
  port?: number; // For server-style instances
  cors?: {
    origin?: string | string[];
    methods?: string[];
    allowHeaders?: string[];
    exposeHeaders?: string[];
    credentials?: boolean;
    maxAge?: number;
  };
}

// Request context that's provider agnostic
export interface RequestContext extends Context {
  env: ComputingConfig;
}

export class ComputingError extends Error {
  code = 'COMPUTING_ERROR';
  statusCode = 500;
}

export class ForbiddenError extends Error {
  code = 'FORBIDDEN';
  statusCode = 403;
}

export class NotFoundError extends Error {
  code = 'NOT_FOUND';
  statusCode = 404;
}

export class UnauthorizedError extends Error {
  code = 'UNAUTHORIZED';
  statusCode = 401;
}

export class ValidationError extends Error {
  code = 'VALIDATION_ERROR';
  statusCode = 400;
}
