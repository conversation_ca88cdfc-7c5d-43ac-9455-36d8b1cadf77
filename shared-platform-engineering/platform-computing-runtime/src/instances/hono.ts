import { Logger } from '@beauty-crm/platform-logger';
import {
  createErrorResponse,
  ErrorMessages,
  ResponseCodes,
} from '@beauty-crm/product-responses';
import { serve } from '@hono/node-server';
import type { Context, MiddlewareHandler, Env } from 'hono';
import { Hono } from 'hono';
import type { ContentfulStatusCode } from 'hono/utils/http-status';
import { cors } from '../middleware/cors';
import type {
  BaseComputingInstance,
  ComputingConfig,
  IRouter,
  AppEnv,
} from '../types'; // Import AppEnv

export class Router implements IRouter {
  constructor(private app: Hono<AppEnv>) {} // Use AppEnv

  get(
    path: string,
    handler: (c: Context<AppEnv>) => Promise<Response> | Response, // Use AppEnv
  ): void {
    this.app.get(path, handler);
  }

  post(
    path: string,
    handler: (c: Context<AppEnv>) => Promise<Response> | Response, // Use AppEnv
  ): void {
    this.app.post(path, handler);
  }

  put(
    path: string,
    handler: (c: Context<AppEnv>) => Promise<Response> | Response, // Use AppEnv
  ): void {
    this.app.put(path, handler);
  }

  delete(
    path: string,
    handler: (c: Context<AppEnv>) => Promise<Response> | Response, // Use AppEnv
  ): void {
    this.app.delete(path, handler);
  }

  patch(
    path: string,
    handler: (c: Context<AppEnv>) => Promise<Response> | Response, // Use AppEnv
  ): void {
    this.app.patch(path, handler);
  }

  getApp(): unknown {
    return this.app;
  }
}

export class HonoInstance implements BaseComputingInstance {
  public readonly type = 'hono' as const;
  private app: Hono<AppEnv>; // Use AppEnv
  private server: ReturnType<typeof serve> | null = null;
  private router: Router;

  constructor(private config: ComputingConfig) {
    this.app = new Hono<AppEnv>(); // Use AppEnv
    this.router = new Router(this.app);
    this.setupMiddleware();
    this.setupErrorHandler();
  }

  public getRouter(): IRouter {
    return this.router;
  }

  private setupMiddleware() {
    // Add environment to context
    this.app.use('*', async (c, next) => {
      // This assignment should now be correct with AppEnv extending ComputingConfig and Env
      c.env = { ...this.config, Bindings: this.config } as AppEnv;
      await next();
    });

    // Add CORS if configured
    if (this.config.cors) {
      this.app.use('*', cors(this.config.cors));
    }

    // Add request logging
    this.app.use('*', async (c, next) => {
      const startTime = Date.now();
      const requestId = crypto.randomUUID();

      Logger.info('Incoming request', {
        headers: Object.fromEntries(Object.entries(c.req.raw.headers)),
        method: c.req.method,
        requestId,
        url: c.req.url,
      });

      try {
        await next();

        const duration = Date.now() - startTime;
        Logger.info('Request completed', {
          duration,
          headers: Object.fromEntries(Object.entries(c.res.headers)),
          requestId,
          status: c.res.status,
        });
      } catch (error) {
        const duration = Date.now() - startTime;
        Logger.error('Request failed', {
          duration,
          error: error instanceof Error ? error.message : 'Unknown error',
          requestId,
          stack: error instanceof Error ? error.stack : undefined,
        });
        throw error;
      }
    });
  }

  private setupErrorHandler() {
    this.app.onError((err, c) => {
      Logger.error('Error handling request', {
        error: err instanceof Error ? err.message : 'Unknown error',
        stack: err instanceof Error ? err.stack : undefined,
      });

      const code = err instanceof Error ? err.name : 'INTERNAL_SERVER_ERROR';
      const message =
        err instanceof Error
          ? err.message
          : ErrorMessages.INTERNAL_SERVER_ERROR;

      return c.json(createErrorResponse(message), this.getStatusCode(code));
    });
  }

  private getStatusCode(code: string): ContentfulStatusCode {
    switch (code) {
      case 'ValidationError':
        return ResponseCodes.BAD_REQUEST as ContentfulStatusCode;
      case 'NotFoundError':
        return ResponseCodes.NOT_FOUND as ContentfulStatusCode;
      case 'UnauthorizedError':
        return ResponseCodes.UNAUTHORIZED as ContentfulStatusCode;
      case 'ForbiddenError':
        return ResponseCodes.FORBIDDEN as ContentfulStatusCode;
      default:
        return ResponseCodes.INTERNAL_SERVER_ERROR as ContentfulStatusCode;
    }
  }

  public use(path: string, ...handlers: MiddlewareHandler[]) {
    // handlers.forEach((handler) => {
    //   this.app.use(path, handler);
    // });
    for (const handler of handlers) {
      this.app.use(path, handler);
    }
  }

  public async start(): Promise<void> {
    const port = this.config.port || 3000;
    this.server = serve({
      fetch: this.app.fetch,
      port,
    });

    Logger.info('Server started', { port });
  }

  public async stop(): Promise<void> {
    if (this.server) {
      await this.server.close();
      this.server = null;
      Logger.info('Server stopped');
    }
  }

  public getApp(): Hono<AppEnv> {
    // Use AppEnv
    return this.app;
  }
}
