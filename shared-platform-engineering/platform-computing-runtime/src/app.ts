import type { <PERSON>ware<PERSON>and<PERSON> } from 'hono';
import { Hono } from 'hono';
import type { ContentfulStatusCode } from 'hono/utils/http-status';
import {
  type ComputingConfig,
  ComputingError,
  ForbiddenError,
  NotFoundError,
  UnauthorizedError,
  ValidationError,
  type AppEnv, // Import AppEnv
} from './types';

export interface AppConfig {
  env: AppEnv; // Use AppEnv here
  middleware?: MiddlewareHandler[];
}

export function createApp(config: AppConfig) {
  const app = new Hono<AppEnv>(); // Use AppEnv here

  // Add environment to context
  app.use('*', async (c, next) => {
    c.env = config.env; // This assignment should now be correct
    await next();
  });

  // Add custom middleware
  if (config.middleware) {
    for (const middleware of config.middleware) {
      app.use('*', middleware);
    }
  }

  // Global error handler
  app.onError((err, c) => {
    console.error('Error:', err);

    if (err instanceof ValidationError) {
      return c.json(
        { code: err.code, message: err.message },
        err.statusCode as unknown as ContentfulStatusCode,
      );
    }

    if (err instanceof NotFoundError) {
      return c.json(
        { code: err.code, message: err.message },
        err.statusCode as unknown as ContentfulStatusCode,
      );
    }

    if (err instanceof UnauthorizedError) {
      return c.json(
        { code: err.code, message: err.message },
        err.statusCode as unknown as ContentfulStatusCode,
      );
    }

    if (err instanceof ForbiddenError) {
      return c.json(
        { code: err.code, message: err.message },
        err.statusCode as unknown as ContentfulStatusCode,
      );
    }

    if (err instanceof ComputingError) {
      return c.json(
        { code: err.code, message: err.message },
        err.statusCode as unknown as ContentfulStatusCode,
      );
    }

    // Default error response
    return c.json(
      {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal Server Error',
      },
      500,
    );
  });

  return app;
}
