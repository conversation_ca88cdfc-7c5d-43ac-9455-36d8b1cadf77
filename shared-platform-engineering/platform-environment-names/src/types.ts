export type ApplicationEnvironment = {
  databaseUrl: string;
  nodeEnv: 'dev' | 'prod' | 'test';
  queues: {
    [key: string]: {
      send: (data: unknown) => Promise<void>;
    };
  };
  [key: string]: unknown;
};

// Generic interface that matches PrismaClient shape without importing it
export interface PrismaClientType {
  $connect(): Promise<void>;
  $disconnect(): Promise<void>;
  [key: string]: unknown;
}
