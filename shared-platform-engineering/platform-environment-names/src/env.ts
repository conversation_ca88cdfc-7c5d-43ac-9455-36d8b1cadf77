// @ts-ignore: Module '@prisma/client' not found; ensure it's installed in the umbrella project
// import type { PrismaClient } from '@beauty-crm/platform-db-client';

// import type {} from '@beauty-crm/platform-computing-lifecycle';

import type { PrismaClientType } from './types';

export type ApplicationEnvironment =
  | 'dev'
  | 'stage'
  | 'prod'
  | 'local'
  | 'test';

export interface ApplicationInitializationEnvironment {
  // Environment
  applicationEnvironment: ApplicationEnvironment; // Renamed from APPLICATION_ENVIRONMENT
  debug: boolean; // Renamed from DEBUG

  // Domains
  publicIdentityManagementDomain: string; // Renamed from PUBLIC_IDENTITY_MANAGEMENT_DOMAIN
  staffManagementDomain: string; // Renamed from STAFF_MANAGEMENT_DOMAIN
  salonManagementDomain: string; // Renamed from SALON_MANAGEMENT_DOMAIN
  inventoryManagementDomain: string; // Renamed from INVENTORY_MANAGEMENT_DOMAIN
  appointmentManagementDomain: string; // Renamed from APPOINTMENT_MANAGEMENT_DOMAIN

  // Database
  db: PrismaClientType; // Renamed from DB

  // Authentication
  authSecret: string; // Renamed from AUTH_SECRET

  // Queues
  // QUEUES: {
  //   SALON_CREATION: CFQueue<{ salonId: string; template: unknown }>;
  //   EMAIL_NOTIFICATION: CFQueue<{ to: string; subject: string; body: string }>;
  //   SMS_NOTIFICATION: CFQueue<{ to: string; message: string }>;
  // };
  // Queues
  queues: {
    salonCreation: unknown; // CFQueue<{ salonId: string; template: unknown }>;
    emailNotification: unknown; //   CFQueue<{ to: string; subject: string; body: string }>;
    smsNotification: unknown; // CFQueue<{ to: string; message: string }>;
  };
  // KV Namespaces
  kv: {
    settings: unknown; //  ;
    cache: unknown; //  ;
  };
}
