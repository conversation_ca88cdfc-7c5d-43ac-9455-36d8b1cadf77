{"dependencies": {"@beauty-crm/product-identity-types": "workspace:*", "bcryptjs": "^3.0.2", "jsonwebtoken": "^9.0.2", "otplib": "^12.0.1", "zod": "^3.25.30"}, "description": "Shared identity client for Beauty CRM", "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^22.15.21", "rimraf": "^6.0.1", "typescript": "^5.8.3"}, "main": "dist/index.js", "name": "@beauty-crm/platform-identity-client", "private": true, "scripts": {"build": "tsc", "clean": "<PERSON><PERSON><PERSON> dist", "format": "biome format --write --fix .", "lint": "biome lint . --config-path ../../biome.json", "lint:check": "biome check . --config-path ../../biome.json", "lint:fix": "biome lint --write . --config-path ../../biome.json"}, "types": "dist/index.d.ts", "version": "1.0.0"}