import type { EventPublisher } from '@beauty-crm/platform-eventing';
import type { Appointment } from '@beauty-crm/product-appointment-types/src/appointment/types';
import {
  createAppointmentCancelledEvent,
  createAppointmentCreatedEvent,
  createAppointmentUpdatedEvent,
} from './events';

type EventCreationOptions = {
  source: string;
  correlationId?: string;
  userId?: string;
};

/**
 * A specialized publisher for appointment-related domain events.
 * It uses the generic EventPublisher from platform-eventing to
 * ensure all events are published consistently through NATS.
 */
export class AppointmentEventPublisher {
  constructor(private readonly eventPublisher: EventPublisher) {}

  /**
   * Publishes an 'appointment.created' event.
   * @param appointment The newly created appointment.
   * @param options Metadata for the event.
   */
  async publishAppointmentCreated(
    appointment: Appointment,
    options: EventCreationOptions,
  ): Promise<void> {
    const event = createAppointmentCreatedEvent(appointment, options);
    await this.eventPublisher.publish(event);
  }

  /**
   * Publishes an 'appointment.updated' event.
   * @param appointment The updated appointment.
   * @param changes The specific fields that were changed.
   * @param options Metadata for the event.
   */
  async publishAppointmentUpdated(
    appointment: Appointment,
    changes: Partial<Appointment>,
    options: EventCreationOptions,
  ): Promise<void> {
    const event = createAppointmentUpdatedEvent(appointment, changes, options);
    await this.eventPublisher.publish(event);
  }

  /**
   * Publishes an 'appointment.cancelled' event.
   * @param appointmentId The ID of the cancelled appointment.
   * @param reason The reason for cancellation.
   * @param options Metadata for the event.
   */
  async publishAppointmentCancelled(
    appointmentId: string,
    reason: string,
    options: EventCreationOptions,
  ): Promise<void> {
    const event = createAppointmentCancelledEvent(
      appointmentId,
      reason,
      options,
    );
    await this.eventPublisher.publish(event);
  }
}
