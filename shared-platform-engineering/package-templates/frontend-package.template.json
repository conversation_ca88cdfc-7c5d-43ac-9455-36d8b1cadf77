{"dependencies": {"@beauty-crm/platform-introvertic-ui": "workspace:*", "@tanstack/react-query": "^5.80.5", "axios": "^1.7.9", "clsx": "^2.1.1", "lucide-react": "^0.513.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.2", "tailwind-merge": "^2.5.4", "zod": "^3.25.51"}, "devDependencies": {"@tailwindcss/vite": "^4.1.9", "@types/node": "^22.15.29", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.3.4", "esbuild": "^0.25.5", "tailwindcss": "^4.1.9", "typescript": "^5.8.3", "vite": "^6.3.5", "vitest": "^3.2.1"}, "name": "{SERVICE_NAME}-frontend", "overrides": {"esbuild": "^0.25.5"}, "private": true, "scripts": {"build": "tsc && vite build", "dev": "vite", "format": "biome format --write --fix .", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "vitest", "test:coverage": "vitest run --coverage"}, "type": "module", "version": "0.0.0"}