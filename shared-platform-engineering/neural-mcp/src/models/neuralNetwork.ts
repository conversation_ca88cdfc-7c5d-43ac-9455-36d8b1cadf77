/**
 * Neural network model for the Master Control Program
 * Detects patterns across service events and predicts potential issues
 */

import type {
  NeuralNetworkState,
  PredictionResult,
  ServiceEvent,
} from '../types/types';

export class MCPNeuralNetwork {
  private state: NeuralNetworkState;
  private eventBuffer: ServiceEvent[] = [];
  private readonly MAX_BUFFER_SIZE = 10000;

  constructor(inputSize = 100, hiddenSize = 60, outputSize = 20) {
    // Initialize neural network with Xavier initialization
    this.state = {
      biases: new Float32Array(hiddenSize + outputSize),
      hiddenSize,
      inputSize,
      outputSize,
      weights: new Float32Array(
        inputSize * hiddenSize + hiddenSize * outputSize,
      ),
    };

    this.initializeWeights();

    console.log('Neural network initialized with dimensions:', {
      hiddenSize,
      inputSize,
      outputSize,
    });
  }

  private initializeWeights(): void {
    const weightsInput = this.state.inputSize * this.state.hiddenSize;
    const sigma1 = Math.sqrt(2 / this.state.inputSize);
    const sigma2 = Math.sqrt(2 / this.state.hiddenSize);

    // Initialize input-hidden weights
    for (let i = 0; i < weightsInput; i++) {
      this.state.weights[i] = (Math.random() * 2 - 1) * sigma1;
    }

    // Initialize hidden-output weights
    for (let i = weightsInput; i < this.state.weights.length; i++) {
      this.state.weights[i] = (Math.random() * 2 - 1) * sigma2;
    }

    // Initialize biases
    for (let i = 0; i < this.state.biases.length; i++) {
      this.state.biases[i] = 0;
    }
  }

  /**
   * Reset the neural network to initial state
   */
  public resetNetwork(): void {
    // Reset weights to initial values
    this.initializeWeights();

    // Clear event buffer
    this.eventBuffer = [];

    console.log('Neural network has been reset to initial state');
  }

  /**
   * Extract features from service events for neural network processing
   */
  private extractFeatures(events: ServiceEvent[]): Float32Array {
    const features = new Float32Array(this.state.inputSize).fill(0);

    if (events.length === 0) return features;

    // Use most recent events for feature extraction
    const recentEvents = events.slice(-Math.min(events.length, 100));

    // Extract features based on service event patterns
    let idx = 0;

    // Feature set 1: Event type frequency (10 features)
    const eventTypeCounts: Record<string, number> = {};
    for (const event of recentEvents) {
      eventTypeCounts[event.type] = (eventTypeCounts[event.type] || 0) + 1;
    }

    // Get the top 10 event types
    const topEventTypes = Object.entries(eventTypeCounts)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10);

    for (let i = 0; i < 10; i++) {
      if (i < topEventTypes.length) {
        features[idx++] = topEventTypes[i][1] / recentEvents.length;
      } else {
        features[idx++] = 0;
      }
    }

    // Feature set 2: Service health metrics (10 features)
    const serviceHealthMetrics: Record<string, number[]> = {};
    for (const event of recentEvents) {
      if (event.metrics && event.serviceId) {
        if (!serviceHealthMetrics[event.serviceId]) {
          serviceHealthMetrics[event.serviceId] = [];
        }

        if (event.metrics.cpuUsage) {
          serviceHealthMetrics[event.serviceId].push(event.metrics.cpuUsage);
        }
      }
    }

    // Calculate average CPU usage for top 10 services
    const topServices = Object.entries(serviceHealthMetrics)
      .sort((a, b) => b[1].length - a[1].length)
      .slice(0, 10);

    for (let i = 0; i < 10; i++) {
      if (i < topServices.length) {
        const avgCpuUsage =
          topServices[i][1].reduce((a, b) => a + b, 0) /
          topServices[i][1].length;
        features[idx++] = avgCpuUsage;
      } else {
        features[idx++] = 0;
      }
    }

    // Feature set 3: Error rates (10 features)
    const errorRates: Record<string, { total: number; errors: number }> = {};
    for (const event of recentEvents) {
      if (!event.serviceId) continue;

      if (!errorRates[event.serviceId]) {
        errorRates[event.serviceId] = { errors: 0, total: 0 };
      }

      errorRates[event.serviceId].total++;
      if (event.status === 'error') {
        errorRates[event.serviceId].errors++;
      }
    }

    // Calculate error rates for top 10 services
    const serviceErrorRates = Object.entries(errorRates)
      .map(([serviceId, counts]) => ({
        errorRate: counts.errors / counts.total,
        serviceId,
      }))
      .sort((a, b) => b.errorRate - a.errorRate)
      .slice(0, 10);

    for (let i = 0; i < 10; i++) {
      if (i < serviceErrorRates.length) {
        features[idx++] = serviceErrorRates[i].errorRate;
      } else {
        features[idx++] = 0;
      }
    }

    // Feature set 4: Response times (10 features)
    const responseTimes: Record<string, number[]> = {};
    for (const event of recentEvents) {
      if (event.metrics?.responseTimeMs && event.serviceId) {
        if (!responseTimes[event.serviceId]) {
          responseTimes[event.serviceId] = [];
        }

        responseTimes[event.serviceId].push(event.metrics.responseTimeMs);
      }
    }

    // Calculate average response times for top 10 services
    const avgResponseTimes = Object.entries(responseTimes)
      .map(([serviceId, times]) => ({
        avgTime: times.reduce((a, b) => a + b, 0) / times.length,
        serviceId,
      }))
      .sort((a, b) => b.avgTime - a.avgTime)
      .slice(0, 10);

    for (let i = 0; i < 10; i++) {
      if (i < avgResponseTimes.length) {
        // Normalize to 0-1 range (assuming 5000ms is max reasonable response time)
        features[idx++] = Math.min(avgResponseTimes[i].avgTime / 5000, 1);
      } else {
        features[idx++] = 0;
      }
    }

    // More feature sets can be added to fully utilize the inputSize

    return features;
  }

  /**
   * Forward pass through the neural network
   */
  private forward(input: Float32Array): Float32Array {
    const hiddenLayer = new Float32Array(this.state.hiddenSize);
    const output = new Float32Array(this.state.outputSize);

    // Input to hidden layer
    for (let i = 0; i < this.state.hiddenSize; i++) {
      let sum = this.state.biases[i];
      for (let j = 0; j < this.state.inputSize; j++) {
        sum += input[j] * this.state.weights[i * this.state.inputSize + j];
      }
      hiddenLayer[i] = Math.max(0, sum); // ReLU activation
    }

    // Hidden to output layer
    const offset = this.state.inputSize * this.state.hiddenSize;
    for (let i = 0; i < this.state.outputSize; i++) {
      let sum = this.state.biases[this.state.hiddenSize + i];
      for (let j = 0; j < this.state.hiddenSize; j++) {
        sum +=
          hiddenLayer[j] *
          this.state.weights[offset + i * this.state.hiddenSize + j];
      }
      output[i] = 1 / (1 + Math.exp(-sum)); // Sigmoid activation
    }

    return output;
  }

  /**
   * Record a new service event
   */
  public recordEvent(event: ServiceEvent): void {
    this.eventBuffer.push(event);

    // Keep buffer size manageable
    if (this.eventBuffer.length > this.MAX_BUFFER_SIZE) {
      this.eventBuffer.shift();
    }

    // Optional: Periodically save events to persistent storage
  }

  /**
   * Train the network on labeled data
   */
  public trainNetwork(events: ServiceEvent[], issueLabels: string[]): void {
    if (events.length < 10) return;

    const features = this.extractFeatures(events);

    // Prepare target outputs
    const issueTypes = [
      'database_connection_failure',
      'api_timeout',
      'memory_leak',
      'cache_miss',
      'authentication_failure',
      'authorization_failure',
      'database_query_degradation',
      'network_partition',
      'service_overload',
      'resource_exhaustion',
      'data_inconsistency',
      'concurrency_conflict',
      'stale_data',
      'invalid_configuration',
      'message_queue_overflow',
      'background_task_failure',
      'disk_space_critical',
      'connection_pool_exhaustion',
      'rate_limit_exceeded',
      'service_dependency_failure',
    ];

    const targetOutput = new Float32Array(this.state.outputSize).fill(0);

    // Set target values based on issue labels
    for (const issueLabel of issueLabels) {
      const index = issueTypes.indexOf(issueLabel);
      if (index !== -1 && index < targetOutput.length) {
        targetOutput[index] = 1.0;
      }
    }

    // Forward pass to get current predictions
    const learningRate = 0.05;
    const output = this.forward(features);

    // Update output layer weights with simple gradient descent
    const weightsInput = this.state.inputSize * this.state.hiddenSize;
    for (let i = 0; i < this.state.outputSize; i++) {
      const error = targetOutput[i] - output[i];

      // Update bias for output layer
      this.state.biases[this.state.hiddenSize + i] += learningRate * error;

      // Update weights for output layer
      for (let j = 0; j < this.state.hiddenSize; j++) {
        const hiddenOutput = Math.max(0, features[j]);
        const weightIndex = weightsInput + i * this.state.hiddenSize + j;
        this.state.weights[weightIndex] += learningRate * error * hiddenOutput;
      }
    }

    console.log(
      'Neural network training completed with',
      events.length,
      'events and',
      issueLabels.length,
      'issue labels',
    );
  }

  /**
   * Make predictions based on current event buffer
   */
  public predictIssues(): PredictionResult[] {
    if (this.eventBuffer.length < 10) {
      return [];
    }

    const features = this.extractFeatures(this.eventBuffer);
    const predictions = this.forward(features);

    // Define issue types the network can detect
    const issueTypes = [
      'database_connection_failure',
      'api_timeout',
      'memory_leak',
      'cache_miss',
      'authentication_failure',
      'authorization_failure',
      'database_query_degradation',
      'network_partition',
      'service_overload',
      'resource_exhaustion',
      'data_inconsistency',
      'concurrency_conflict',
      'stale_data',
      'invalid_configuration',
      'message_queue_overflow',
      'background_task_failure',
      'disk_space_critical',
      'connection_pool_exhaustion',
      'rate_limit_exceeded',
      'service_dependency_failure',
    ];

    const results: PredictionResult[] = [];

    // Create predictions for issues with confidence above threshold
    for (let i = 0; i < predictions.length; i++) {
      if (predictions[i] > 0.3) {
        // Threshold for prediction
        results.push({
          confidence: predictions[i],
          issueType: issueTypes[i],
          relatedEvents: this.findRelatedEvents(issueTypes[i]),
          services: this.getAffectedServices(issueTypes[i]),
          suggestedActions: this.getSuggestedActions(issueTypes[i]),
        });
      }
    }

    if (results.length > 0) {
      console.log(
        `Neural network detected ${results.length} potential issues:`,
        results
          .map(
            (r) =>
              `${r.issueType} (confidence: ${(r.confidence * 100).toFixed(1)}%)`,
          )
          .join(', '),
      );
    }

    return results;
  }

  /**
   * Identify services affected by the predicted issue
   */
  private getAffectedServices(issueType: string): string[] {
    // Analyze which services are most likely affected by this issue
    const recentEvents = this.eventBuffer.slice(-100);
    const serviceAggregates: Record<string, { count: number; errors: number }> =
      {};

    // Filter events by issue type if possible
    const relevantEvents = issueType.includes('database')
      ? recentEvents.filter(
          (e) => e.message?.includes('database') || e.type.includes('database'),
        )
      : recentEvents;

    for (const event of relevantEvents) {
      if (!event.serviceId) continue;

      if (!serviceAggregates[event.serviceId]) {
        serviceAggregates[event.serviceId] = { count: 0, errors: 0 };
      }

      serviceAggregates[event.serviceId].count++;

      if (event.status === 'error') {
        serviceAggregates[event.serviceId].errors++;
      }
    }

    // Sort services by error rate
    return Object.entries(serviceAggregates)
      .sort((a, b) => b[1].errors / b[1].count - a[1].errors / a[1].count)
      .slice(0, 3)
      .map(([serviceId]) => serviceId);
  }

  /**
   * Get suggested actions for addressing the predicted issue
   */
  private getSuggestedActions(issueType: string): string[] {
    switch (issueType) {
      case 'database_connection_failure':
        return [
          'Check database server status',
          'Verify connection pool settings',
          'Implement retry mechanism with exponential backoff',
        ];
      case 'api_timeout':
        return [
          'Increase timeout thresholds',
          'Implement circuit breaker pattern',
          'Check server resource utilization',
        ];
      case 'memory_leak':
        return [
          'Review recent deployments for memory management issues',
          'Check for proper cleanup in event handlers and subscriptions',
          'Implement memory usage monitoring and alerts',
        ];
      case 'service_dependency_failure':
        return [
          'Implement fallback mechanisms',
          'Add retry logic with exponential backoff',
          'Configure appropriate timeout values',
        ];
      case 'service_overload':
        return [
          'Scale out the affected service',
          'Implement rate limiting',
          'Optimize resource-intensive operations',
        ];
      default:
        return [
          'Monitor affected services',
          'Check recent logs for error patterns',
          'Review system metrics for anomalies',
        ];
    }
  }

  /**
   * Find events related to the predicted issue
   */
  private findRelatedEvents(issueType: string): ServiceEvent[] {
    // Find the most relevant events for the given issue type
    const recentEvents = this.eventBuffer.slice(-100);

    switch (issueType) {
      case 'database_connection_failure':
        return recentEvents
          .filter(
            (e) =>
              e.status === 'error' &&
              (e.message?.includes('database') ||
                e.message?.includes('connection') ||
                e.message?.includes('sql')),
          )
          .slice(-5);

      case 'api_timeout':
        return recentEvents
          .filter(
            (e) =>
              e.status === 'error' &&
              (e.message?.includes('timeout') ||
                (e.metrics?.responseTimeMs && e.metrics.responseTimeMs > 5000)),
          ) // Added closing parenthesis for the filter
          .slice(-5);

      case 'memory_leak': // Added case for memory_leak
        return recentEvents
          .filter((e) => e.metrics?.memoryUsage && e.metrics.memoryUsage > 0.8)
          .slice(-5);

      default:
        return recentEvents.filter((e) => e.status === 'error').slice(-5);
    }
  }
}
