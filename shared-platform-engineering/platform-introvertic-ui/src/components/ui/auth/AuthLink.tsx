import { Container } from '@/components/ui/container/Container';
import { Typography } from '@/components/ui/typography/Typography';
import { cn } from '@/lib/utils';

interface LinkComponentProps {
  to: string;
  className?: string;
  children: React.ReactNode;
}

interface AuthLinkProps extends React.HTMLAttributes<HTMLDivElement> {
  text: string;
  linkText: string;
  href: string;
  linkComponent: React.ComponentType<LinkComponentProps>;
}

export function AuthLink({
  text,
  linkText,
  href,
  linkComponent: LinkComponent,
  className,
  ...props
}: AuthLinkProps) {
  return (
    <Container
      direction="row"
      spacing="sm"
      justify="center"
      align="center"
      className={cn(className)}
      {...props}
    >
      <Typography variant="p">{text}</Typography>
      <LinkComponent
        to={href}
        className="font-medium text-primary hover:text-primary/90 hover:underline"
      >
        {linkText}
      </LinkComponent>
    </Container>
  );
}
