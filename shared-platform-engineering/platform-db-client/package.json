{"author": "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "dependencies": {"@beauty-crm/platform-environment-names": "workspace:*", "@beauty-crm/platform-logger": "workspace:*", "@beauty-crm/product-db-names": "workspace:*", "@beauty-crm/product-domain-types": "workspace:*", "uuid": "^11.1.0"}, "description": "Database infrastructure and connection management utilities for platform services", "devDependencies": {"@types/uuid": "^10.0.0", "rimraf": "^6.0.1", "typescript": "^5.8.3"}, "keywords": [], "license": "ISC", "main": "dist/index.js", "name": "@beauty-crm/platform-db-client", "peerDependencies": {"@prisma/client": "^6.9.0"}, "private": true, "scripts": {"build": "tsc", "clean": "<PERSON><PERSON><PERSON> dist", "dev": "tsc --watch", "format": "biome format --write --fix .", "lint": "biome lint .", "lint:check": "biome check .", "lint:fix": "biome lint --write .", "outdated": "bun outdated", "postinstall": "node validate-dependencies.cjs", "test": "echo 'No tests specified for platform-db-client' && exit 0"}, "type": "module", "types": "dist/index.d.ts", "version": "1.0.0"}