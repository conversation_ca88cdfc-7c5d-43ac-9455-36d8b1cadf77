{"dependencies": {"@beauty-crm/platform-computing-lifecycle": "workspace:*", "@beauty-crm/platform-environment-names": "workspace:*", "@beauty-crm/product-domain-types": "workspace:*", "@prisma/client": "^6.9.0", "vitest": "^3.1.4"}, "devDependencies": {"@types/node": "^22.15.21", "rimraf": "^6.0.1", "typescript": "^5.8.3"}, "main": "dist/index.js", "name": "@beauty-crm/platform-test-runner", "private": true, "scripts": {"_prebuild": "computing-lifecycle generate tsconfig", "build": "tsc", "clean": "<PERSON><PERSON><PERSON> dist", "format": "biome format --write --fix .", "lint": "biome lint .", "lint:check": "biome check .", "lint:fix": "biome lint --write .", "test": "echo 'No tests specified for platform-test-runner' && exit 0"}, "types": "dist/index.d.ts", "version": "1.0.0"}