import type {
  ApplicationInitializationEnvironment,
  PrismaClientType,
} from '@beauty-crm/platform-environment-names';

export function createTestEnv(): ApplicationInitializationEnvironment {
  return {
    applicationEnvironment: 'test',
    appointmentManagementDomain: 'test.appointment.com',
    authSecret: 'test-secret',
    db: {} as PrismaClientType,
    debug: true,
    inventoryManagementDomain: 'test.inventory.com',
    kv: {
      cache: {},
      settings: {},
    },
    publicIdentityManagementDomain: 'test.identity.com',
    queues: {
      emailNotification: {},
      salonCreation: {},
      smsNotification: {},
    },
    staffManagementDomain: 'test.staff.com',
    salonManagementDomain: 'test.salon.com',
    // SALON_MANAGEMENT_DOMAIN: 'test.salon.com',
    // STAFF_MANAGEMENT_DOMAIN: 'test.staff.com',
  };
}

export function createTestRequest(
  path: string,
  options: RequestInit & { hostname?: string } = {},
): Request {
  const url = new URL(path, `http://${options.hostname || 'localhost:8787'}`);
  return new Request(url, {
    method: 'GET',
    ...options,
  });
}

export function createTestAuthHeaders(jwt = 'test-jwt'): Headers {
  return new Headers({
    Authorization: `Bearer ${jwt}`,
  });
}

export function createTestResponse(
  body: unknown,
  init: ResponseInit = {},
): Response {
  return new Response(JSON.stringify(body), {
    headers: { 'Content-Type': 'application/json' },
    ...init,
  });
}
