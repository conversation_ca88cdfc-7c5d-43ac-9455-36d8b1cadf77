// DevTools Sidebar JavaScript
const _selectedElement = null;
const port = chrome.runtime.connect({ name: 'neural-mcp-sidebar' });

// Listen for element selection
chrome.devtools.panels.elements.onSelectionChanged.addListener(() => {
  // Get the currently selected element
  chrome.devtools.inspectedWindow.eval(
    '($0)',
    { useContentScriptContext: true },
    (_result, isException) => {
      if (isException) {
        console.error('Error getting selected element:', isException);
        return;
      }

      // Request information about the selected element
      chrome.devtools.inspectedWindow.eval(
        `
        (function() {
          const el = $0;
          if (!el) return null;
          
          // Get basic element info
          const tagName = el.tagName.toLowerCase();
          const id = el.id ? '#' + el.id : '';
          const classNames = Array.from(el.classList).map(c => '.' + c).join('');
          const selector = tagName + id + classNames;
          
          // Get accessibility info
          const hasAria = Object.keys(el.attributes).some(key => 
            el.attributes[key].name && el.attributes[key].name.startsWith('aria-')
          );
          const role = el.getAttribute('role') || '';
          const hasTabIndex = el.hasAttribute('tabindex');
          
          // Get event listeners (simplified - can't get all listeners this way)
    const eventListeners = [];
    const commonEvents = ['click', 'focus', 'blur', 'change', 'input', 'keydown', 'keyup'];
    for (const event of commonEvents) {
      // This only detects inline event handlers, not addEventListener calls
      if (el['on' + event]) {
        eventListeners.push(event);
      }
    }
          
          // Performance estimation (very basic)
          const childrenCount = el.children.length;
          const depth = (function getDepth(el, current = 0) {
            if (!el.parentElement) return current;
            return getDepth(el.parentElement, current + 1);
          })(el);
          
          // Analysis
          const hasManyChildren = childrenCount > 20;
          const isDeep = depth > 10;
          const isLarge = el.getBoundingClientRect().width * el.getBoundingClientRect().height > 100000;
          
          return {
            selector,
            tagName,
            id: el.id || '',
            classes: Array.from(el.classList),
            attributes: Array.from(el.attributes).map(attr => ({ name: attr.name, value: attr.value })),
            accessibility: {
              hasAria,
              role,
              hasTabIndex
            },
            eventListeners,
            performance: {
              childrenCount,
              depth,
              hasManyChildren,
              isDeep,
              isLarge
            }
          };
        })()
        `,
        (result, isException) => {
          if (isException || !result) {
            console.error('Error analyzing element:', isException);
            return;
          }

          updateElementInfo(result);

          // Send to background script for neural analysis
          port.postMessage({
            action: 'analyzeElement',
            element: result,
          });
        },
      );
    },
  );
});

// Listen for responses from the background script
port.onMessage.addListener((message) => {
  if (message.type === 'neuralAnalysis') {
    updateNeuralInsights(message.data);
  }
});

// Update element information in the sidebar
function updateElementInfo(info) {
  // Update element type
  document.getElementById('element-type').textContent =
    `${info.tagName}${info.id ? ` #${info.id}` : ''}${info.classes.length ? ` .${info.classes.join('.')}` : ''}`;

  // Update accessibility info
  const accessibilityEl = document.getElementById('accessibility-info');
  if (info.accessibility.hasAria || info.accessibility.role) {
    const accessText = [];
    if (info.accessibility.role)
      accessText.push(`Role: ${info.accessibility.role}`);
    if (info.accessibility.hasAria) accessText.push('Has ARIA attributes');
    if (info.accessibility.hasTabIndex) accessText.push('Has tabindex');

    accessibilityEl.innerHTML = accessText.join('<br>');
  } else {
    accessibilityEl.innerHTML = `<span class="badge badge-warning">No a11y attributes</span>`;
  }

  // Update performance info
  const performanceEl = document.getElementById('performance-info');
  const performanceText = [];
  performanceText.push(`Children: ${info.performance.childrenCount}`);
  performanceText.push(`Depth: ${info.performance.depth}`);

  let hasPerfIssue = false;
  if (info.performance.hasManyChildren) {
    performanceText.push(
      `<span class="badge badge-warning">Many children</span>`,
    );
    hasPerfIssue = true;
  }
  if (info.performance.isDeep) {
    performanceText.push(
      `<span class="badge badge-warning">Deep nesting</span>`,
    );
    hasPerfIssue = true;
  }
  if (info.performance.isLarge) {
    performanceText.push(
      `<span class="badge badge-warning">Large element</span>`,
    );
    hasPerfIssue = true;
  }

  if (!hasPerfIssue) {
    performanceText.push(`<span class="badge badge-info">Good</span>`);
  }

  performanceEl.innerHTML = performanceText.join('<br>');

  // Update event listeners
  const eventListenersEl = document.getElementById('event-listeners');
  const eventCountEl = document.getElementById('event-count');

  if (info.eventListeners?.length) {
    eventCountEl.textContent = info.eventListeners.length;

    let html = '';
    for (const event of info.eventListeners) {
      html += `<div class="property-row">
        <div class="property-name">${event}</div>
        <div class="property-value"><span class="highlight">has handler</span></div>
      </div>`;
    }

    eventListenersEl.innerHTML = html;
  } else {
    eventCountEl.textContent = '0';
    eventListenersEl.innerHTML =
      '<div class="message">No event listeners detected</div>';
  }
}

// Update neural insights
function updateNeuralInsights(insights) {
  const neuralInsightsEl = document.getElementById('neural-insights');

  if (!insights || Object.keys(insights).length === 0) {
    neuralInsightsEl.innerHTML =
      '<div class="message">No insights available</div>';
    return;
  }

  let html = '';

  // Display recommendations
  if (insights.recommendations?.length) {
    // insights.recommendations.forEach((rec) => {
    //   const badgeClass =
    //     rec.severity === 'high'
    //       ? 'badge-error'
    //       : rec.severity === 'medium'
    //         ? 'badge-warning'
    //         : 'badge-info';
    //   html += `<div class="property-row">
    //     <div class="property-value">
    //       <span class="badge ${badgeClass}">${rec.category}</span>
    //       ${rec.message}
    //     </div>
    //   </div>`;
    // });
  } else {
    html = '<div class="message">No recommendations available</div>';
  }

  neuralInsightsEl.innerHTML = html;
}

// Initialize with empty state
updateElementInfo({
  accessibility: {
    hasAria: false,
    hasTabIndex: false,
    role: '',
  },
  attributes: [],
  classes: [],
  eventListeners: [],
  id: '',
  performance: {
    childrenCount: 0,
    depth: 0,
    hasManyChildren: false,
    isDeep: false,
    isLarge: false,
  },
  tagName: '',
});
