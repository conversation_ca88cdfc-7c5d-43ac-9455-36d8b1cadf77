/**
 * Beauty CRM Neural MCP Connector
 * Background Service Worker
 */

// Configuration
let config = {
  healthEndpoint: 'http://localhost:3100/api/health',
  isEnabled: true,
  maxEventBuffer: 100,
  mcpEndpoint: 'http://localhost:3100/api/events', // 5 seconds
  sendInterval: 5000,
};

// Event buffer
let eventBuffer = [];
let connectionRetries = 0;
const connectionStatus = {
  isConnected: false,
  lastAttempt: null,
  lastSuccess: null,
};

// Stats for popup
const stats = {
  errorsDetected: 0,
  eventsBuffered: 0,
  eventsSent: 0,
  lastSent: null,
};

// Storage for DevTools panels
const devtoolsConnections = {};
const requestsHistory = [];
const errorsHistory = [];

// Maximum history size
const MAX_HISTORY_SIZE = 500;

// Load configuration from storage
chrome.storage.sync.get(['mcpConfig'], (result) => {
  if (result.mcpConfig) {
    config = { ...config, ...result.mcpConfig };
    console.log('MCP configuration loaded:', config);
  } else {
    // Save default config
    chrome.storage.sync.set({ mcpConfig: config });
  }
});

/**
 * Check if the MCP server is available
 */
async function checkMCPConnection() {
  connectionStatus.lastAttempt = Date.now();

  // Try the health endpoint first
  try {
    console.log(`Checking MCP connection at: ${config.healthEndpoint}`);

    const response = await fetch(config.healthEndpoint, {
      headers: {
        'Content-Type': 'application/json',
      },
      method: 'GET',
      // Add timeout to prevent long hanging requests
      signal: AbortSignal.timeout(5000),
    });

    if (response.ok) {
      const data = await response.json();

      if (data.status === 'ok') {
        console.log('MCP server connection successful');
        connectionStatus.isConnected = true;
        connectionStatus.lastSuccess = Date.now();
        connectionRetries = 0;
        return true;
      }
    }

    throw new Error('Health check failed');
  } catch (error) {
    // If health endpoint fails, try a simple ping to the base URL
    try {
      const baseUrl = config.mcpEndpoint.split('/api/')[0];

      console.log(`Health check failed, trying base URL: ${baseUrl}`);

      const pingResponse = await fetch(baseUrl, {
        method: 'GET',
        signal: AbortSignal.timeout(3000),
      });

      if (pingResponse.ok) {
        console.log('MCP server base URL is reachable');
        connectionStatus.isConnected = true;
        connectionStatus.lastSuccess = Date.now();
        connectionRetries = 0;
        return true;
      }
    } catch (pingError) {
      console.error('Base URL ping failed:', pingError);
    }

    // Both attempts failed
    connectionRetries++;
    connectionStatus.isConnected = false;
    console.error(
      `MCP server connection failed (attempt ${connectionRetries}):`,
      error,
    );

    return false;
  }
}

/**
 * Send events to the MCP server
 */
async function sendEvents() {
  if (!config.isEnabled || eventBuffer.length === 0) {
    return;
  }

  // Store the events at function scope level so it's available in both try and catch blocks
  const events = [...eventBuffer];
  eventBuffer = [];

  try {
    // Use a timeout signal to prevent hanging requests
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10s timeout

    const batchEndpoint = `${config.mcpEndpoint}/batch`;
    console.log(
      `Attempting to send ${events.length} events to MCP at ${batchEndpoint}`,
    );

    // Log the first event for debugging
    if (events.length > 0) {
      console.log('First event in batch:', JSON.stringify(events[0]));
    }

    const requestBody = JSON.stringify({
      events: events,
      sourceId: 'chrome-extension',
    });
    console.log('Request body size:', requestBody.length, 'bytes');

    const response = await fetch(batchEndpoint, {
      body: requestBody,
      headers: {
        'Content-Type': 'application/json',
      },
      method: 'POST',
      signal: controller.signal,
    });

    // Clear the timeout
    clearTimeout(timeoutId);

    console.log('Response status:', response.status, response.statusText);

    if (!response.ok) {
      throw new Error(`HTTP error ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    console.log('Response data:', JSON.stringify(data));

    if (data.success) {
      console.log(`Successfully sent ${data.data.processed} events to MCP`);
      stats.eventsSent += events.length;
      stats.lastSent = Date.now();
      chrome.storage.local.set({ mcpStats: stats });
    } else {
      console.error('Failed to send events to MCP:', data.error);
      // Put events back in the buffer for retry
      eventBuffer = [...events, ...eventBuffer].slice(0, config.maxEventBuffer);
      stats.eventsBuffered = eventBuffer.length;
      chrome.storage.local.set({ mcpStats: stats });
    }
  } catch (error) {
    console.error('Error sending events to MCP:', error);
    // Put events back in the buffer for retry
    eventBuffer = [...events, ...eventBuffer].slice(0, config.maxEventBuffer);
    stats.eventsBuffered = eventBuffer.length;
    chrome.storage.local.set({ mcpStats: stats });
  }
}

/**
 * Record an event in the buffer
 */
function recordEvent(event) {
  if (!config.isEnabled) {
    return;
  }

  // Ensure event status is one of the valid values: 'success', 'error', 'warning', 'info'
  if (
    !event.status ||
    !['success', 'error', 'warning', 'info'].includes(event.status)
  ) {
    event.status = event.status === 'error' ? 'error' : 'info';
  }

  // If metrics isn't an object or is null, initialize it
  if (!event.metrics || typeof event.metrics !== 'object') {
    event.metrics = {};
  }

  // Ensure metrics are numbers
  if (
    event.metrics.responseTimeMs !== undefined &&
    typeof event.metrics.responseTimeMs !== 'number'
  ) {
    event.metrics.responseTimeMs =
      Number.parseFloat(event.metrics.responseTimeMs) || 0;
  }

  eventBuffer.push({
    ...event,
    id: generateUUID(),
    timestamp: Date.now(),
  });

  // Prevent buffer overflow
  if (eventBuffer.length > config.maxEventBuffer) {
    eventBuffer.shift();
  }
}

/**
 * Generate a UUID v4
 */
function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    const r = (Math.random() * 16) | 0;
    const v = c === 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}

// Set up event listeners
chrome.webNavigation.onCompleted.addListener((details) => {
  if (details.frameId === 0) {
    // Only record main frame navigations
    recordEvent({
      context: { tabId: details.tabId },
      durationMs: Number.parseInt(details.timeStamp) || 0,
      endpoint: details.url,
      status: 'success',
      type: 'navigation',
    });
  }
});

// Track web requests
chrome.webRequest.onCompleted.addListener(
  (details) => {
    const timestamp = Date.now();

    // Only include main frame and relevant resource types
    if (
      details.type !== 'main_frame' &&
      details.type !== 'xmlhttprequest' &&
      details.type !== 'fetch'
    ) {
      return;
    }

    // Create a request record
    const request = {
      contentType: details.responseHeaders
        ? details.responseHeaders.find(
            (h) => h.name.toLowerCase() === 'content-type',
          )?.value
        : undefined,
      id: generateUUID(),
      method: details.method,
      size: details.responseSize,
      status: details.statusCode,
      timestamp: timestamp,
      type: details.type,
      url: details.url,
    };

    // Add to requests history
    requestsHistory.unshift(request);

    // Trim history if needed
    if (requestsHistory.length > MAX_HISTORY_SIZE) {
      requestsHistory.pop();
    }

    // Record HTTP errors
    if (details.statusCode >= 400) {
      const error = {
        id: generateUUID(),
        line: null,
        message: `${details.method} ${details.url} returned ${details.statusCode}`,
        source: details.url,
        timestamp: timestamp,
        type: 'HTTP Error',
      };

      errorsHistory.unshift(error);

      // Add to event buffer
      recordEvent({
        error: {
          message: `HTTP ${details.statusCode}`,
          statusCode: details.statusCode,
        },
        timestamp: new Date(timestamp).toISOString(),
        type: 'web_error',
        url: details.url,
      });

      // Update stats
      stats.errorsDetected++;
    }

    // Add to event buffer
    recordEvent({
      method: details.method,
      status: details.statusCode,
      timestamp: new Date(timestamp).toISOString(),
      type: 'web_request',
      url: details.url,
    });

    // Notify DevTools panels
    for (const tabId of Object.keys(devtoolsConnections)) {
      if (devtoolsConnections[tabId].name === 'neural-mcp-devtools') {
        devtoolsConnections[tabId].postMessage({
          data: requestsHistory,
          type: 'requests',
        });
      }
    }
  },
  { urls: ['<all_urls>'] },
  ['responseHeaders'],
);

// Track JavaScript errors via content script messages
chrome.runtime.onMessage.addListener((message, _sender, sendResponse) => {
  if (message.type === 'js_error') {
    const timestamp = Date.now();

    // Create an error record
    const error = {
      id: generateUUID(),
      line: message.error.lineno,
      message: message.error.message,
      source: message.error.filename || message.url,
      timestamp: timestamp,
      type: 'JavaScript Error',
    };

    // Add to errors history
    errorsHistory.unshift(error);

    // Trim history if needed
    if (errorsHistory.length > MAX_HISTORY_SIZE) {
      errorsHistory.pop();
    }

    // Record error event
    recordEvent({
      error: {
        column: message.error.colno,
        line: message.error.lineno,
        message: message.error.message,
        stack: message.error.stack,
      },
      timestamp: new Date(timestamp).toISOString(),
      type: 'js_error',
      url: message.url,
    });

    // Update stats
    stats.errorsDetected++;

    // Notify DevTools panels
    for (const tabId of Object.keys(devtoolsConnections)) {
      if (devtoolsConnections[tabId].name === 'neural-mcp-devtools') {
        devtoolsConnections[tabId].postMessage({
          data: errorsHistory,
          type: 'errors',
        });
      }
    }
  }

  // Always send a response
  sendResponse({ received: true });
});

// Simulated neural analysis for DOM elements
function analyzeElement(element) {
  // In a real implementation, this might call the neural network API
  const recommendations = [];

  // Example analysis based on element properties
  if (
    element.tagName === 'img' &&
    !element.attributes.some((attr) => attr.name === 'alt')
  ) {
    recommendations.push({
      category: 'Accessibility',
      message: 'Image is missing alt text which is required for screen readers',
      severity: 'high',
    });
  }

  if (
    element.tagName === 'button' &&
    !element.attributes.some((attr) => attr.name === 'type')
  ) {
    recommendations.push({
      category: 'Form',
      message:
        'Button is missing type attribute, should be "button", "submit", or "reset"',
      severity: 'medium',
    });
  }

  if (element.performance.hasManyChildren) {
    recommendations.push({
      category: 'Performance',
      message: `Element has ${element.performance.childrenCount} children which may impact rendering performance`,
      severity: 'medium',
    });
  }

  if (element.performance.isDeep) {
    recommendations.push({
      category: 'Structure',
      message: `Element is deeply nested (depth: ${element.performance.depth}), consider simplified DOM structure`,
      severity: 'low',
    });
  }

  return {
    recommendations,
  };
}

// Listen for messages from content scripts or popup
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (!message || !message.type) {
    return;
  }

  switch (message.type) {
    case 'error':
      // Record error event from content script
      recordEvent({
        context: {
          column: message.data.column,
          line: message.data.line,
          stack: message.data.stack,
          tabId: sender.tab?.id,
          url: message.data.url,
        },
        message: message.data.message,
        status: 'error',
        type: 'error',
      });
      stats.errorsDetected++;
      chrome.storage.local.set({ mcpStats: stats });
      sendResponse({ received: true });
      break;

    case 'performance':
      // Record performance event from content script
      recordEvent({
        context: {
          tabId: sender.tab?.id,
          url: message.data.url,
        },
        metrics: message.data.metrics,
        status: 'info',
        type: 'performance',
      });
      sendResponse({ received: true });
      break;

    case 'check_connection':
      // Check if MCP server is available
      checkMCPConnection().then(sendResponse);
      return true; // Required for async sendResponse

    case 'get_config':
      // Return current configuration
      sendResponse({ config });
      break;

    case 'update_config':
      // Update configuration
      config = { ...config, ...message.data };
      chrome.storage.sync.set({ mcpConfig: config });
      sendResponse({ config });
      break;

    case 'get_stats':
      // Return current stats
      stats.eventsBuffered = eventBuffer.length;
      sendResponse({ stats });
      break;
  }
});

// Set up periodic event sending
setInterval(() => {
  sendEvents();
}, config.sendInterval);

// Initial connection check
checkMCPConnection().then((isConnected) => {
  console.log(`MCP server is ${isConnected ? 'connected' : 'disconnected'}`);
});

// Add message listener for DevTools connections
chrome.runtime.onConnect.addListener((port) => {
  // DevTools connection
  if (
    port.name === 'neural-mcp-devtools' ||
    port.name === 'neural-mcp-sidebar'
  ) {
    const tabId = port.sender.tab ? port.sender.tab.id : 'unknown';
    devtoolsConnections[tabId] = port;

    console.log(`DevTools connected: ${port.name} for tab ${tabId}`);

    // Handle messages from DevTools
    port.onMessage.addListener((message) => {
      if (message.action === 'getRequests') {
        port.postMessage({
          data: requestsHistory,
          type: 'requests',
        });
      } else if (message.action === 'getErrors') {
        port.postMessage({
          data: errorsHistory,
          type: 'errors',
        });
      } else if (message.action === 'clearRequests') {
        requestsHistory.length = 0;
        port.postMessage({
          data: requestsHistory,
          type: 'requests',
        });
      } else if (message.action === 'clearErrors') {
        errorsHistory.length = 0;
        port.postMessage({
          data: errorsHistory,
          type: 'errors',
        });
      } else if (message.action === 'analyzeElement') {
        // Perform neural analysis of the element (simulated here)
        const analysis = analyzeElement(message.element);
        port.postMessage({
          data: analysis,
          type: 'neuralAnalysis',
        });
      }
    });

    // Handle disconnect
    port.onDisconnect.addListener(() => {
      delete devtoolsConnections[tabId];
      console.log(`DevTools disconnected: ${port.name} for tab ${tabId}`);
    });

    // Send initial data
    if (port.name === 'neural-mcp-devtools') {
      port.postMessage({
        data: requestsHistory,
        type: 'requests',
      });
      port.postMessage({
        data: errorsHistory,
        type: 'errors',
      });
    }
  }
});
