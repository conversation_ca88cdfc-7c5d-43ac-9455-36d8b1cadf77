/**
 * Beauty CRM Neural MCP Connector
 * Content Script - Runs in the context of web pages
 */

/**
 * Initialize performance monitoring
 */
function initPerformanceMonitoring() {
  // Capture performance metrics when the page loads
  window.addEventListener('load', () => {
    setTimeout(() => {
      const performanceData = collectPerformanceMetrics();
      sendPerformanceData(performanceData);
    }, 0);
  });

  // Set up periodic performance monitoring
  setInterval(() => {
    const performanceData = collectPerformanceMetrics();
    sendPerformanceData(performanceData);
  }, 30000); // Every 30 seconds
}

/**
 * Collect performance metrics from the page
 */
function collectPerformanceMetrics() {
  const metrics = {};

  // Navigation timing
  if (performance.timing) {
    const timing = performance.timing;
    metrics.loadTime = timing.loadEventEnd - timing.navigationStart;
    metrics.domContentLoaded =
      timing.domContentLoadedEventEnd - timing.navigationStart;
    metrics.firstPaint = timing.responseEnd - timing.navigationStart;
    metrics.backendPerformance = timing.responseEnd - timing.requestStart;
  }

  // Memory info if available
  if (performance.memory) {
    metrics.usedJSHeapSize = performance.memory.usedJSHeapSize;
    metrics.totalJSHeapSize = performance.memory.totalJSHeapSize;
    metrics.jsHeapSizeLimit = performance.memory.jsHeapSizeLimit;
  }

  // Resource timing
  const resources = performance.getEntriesByType('resource');
  if (resources.length > 0) {
    // Initialize resource stats
    const resourceStats = {
      slowest: 0,
      slowestUrl: '',
      total: resources.length,
      totalDuration: 0,
      types: {},
    };

    for (const resource of resources) {
      // Changed from forEach to for...of
      // Skip data: and blob: resources
      if (
        resource.name.startsWith('data:') ||
        resource.name.startsWith('blob:')
      ) {
        continue; // Changed from return to continue
      }

      // Calculate total duration
      const duration = resource.duration;
      resourceStats.totalDuration += duration;

      // Find the slowest resource
      if (duration > resourceStats.slowest) {
        resourceStats.slowest = duration;
        resourceStats.slowestUrl = resource.name;
      }

      // Count by resource type
      const type = resource.initiatorType;
      if (!resourceStats.types[type]) {
        resourceStats.types[type] = { count: 0, totalDuration: 0 };
      }
      resourceStats.types[type].count++;
      resourceStats.types[type].totalDuration += duration;
    }

    // Add resource stats to metrics
    metrics.resources = resourceStats;
  }

  return {
    metrics,
    timestamp: Date.now(),
    url: window.location.href,
    userAgent: navigator.userAgent,
  };
}

/**
 * Send performance data to the background script
 */
function sendPerformanceData(performanceData) {
  chrome.runtime.sendMessage({
    data: performanceData,
    type: 'performance',
  });
}

/**
 * Initialize error tracking
 */
function initErrorTracking() {
  // Capture uncaught JavaScript errors
  window.addEventListener('error', (event) => {
    const errorData = {
      column: event.colno,
      line: event.lineno,
      message: event.message,
      stack: event.error ? event.error.stack : '',
      timestamp: Date.now(),
      url: event.filename,
    };

    sendErrorData(errorData);

    // Don't prevent default - let the browser handle the error normally
    return false;
  });

  // Capture unhandled promise rejections
  window.addEventListener('unhandledrejection', (event) => {
    let message = 'Unhandled Promise Rejection';
    let stack = '';

    if (typeof event.reason === 'object') {
      message = event.reason.message || message;
      stack = event.reason.stack || '';
    } else if (typeof event.reason === 'string') {
      message = event.reason;
    }

    const errorData = {
      message: message,
      stack: stack,
      timestamp: Date.now(),
      url: window.location.href,
    };

    sendErrorData(errorData);

    // Don't prevent default - let the browser handle the rejection normally
    return false;
  });
}

/**
 * Send error data to the background script
 */
function sendErrorData(errorData) {
  chrome.runtime.sendMessage({
    data: errorData,
    type: 'error',
  });
}

/**
 * Initialize console error/warning tracking
 */
function initConsoleTracking() {
  // Store original console methods
  const originalError = console.error;
  const originalWarn = console.warn;

  // Override console.error
  console.error = (...args) => {
    // Changed from arguments to ...args
    // Call the original method
    originalError.apply(console, args);

    // Get error message
    const message = args
      .map((arg) => {
        if (typeof arg === 'object') {
          try {
            return JSON.stringify(arg);
          } catch (_e) {
            return String(arg);
          }
        }
        return String(arg);
      })
      .join(' ');

    // Send to background script
    sendErrorData({
      message: `Console Error: ${message}`,
      timestamp: Date.now(),
      type: 'console.error',
      url: window.location.href,
    });
  };

  // Override console.warn
  console.warn = (...args) => {
    // Changed from arguments to ...args
    // Call the original method
    originalWarn.apply(console, args);

    // Get warning message
    const message = args
      .map((arg) => {
        if (typeof arg === 'object') {
          try {
            return JSON.stringify(arg);
          } catch (_e) {
            return String(arg);
          }
        }
        return String(arg);
      })
      .join(' ');

    // Send to background script
    sendErrorData({
      message: `Console Warning: ${message}`,
      timestamp: Date.now(),
      type: 'console.warn',
      url: window.location.href,
    });
  };
}

/**
 * Initialize network request tracking
 */
function initNetworkTracking() {
  // Only track fetch and XHR if we're in a normal page (not extension)
  if (window.location.protocol !== 'chrome-extension:') {
    // Track fetch requests
    if (window.fetch) {
      const originalFetch = window.fetch;
      window.fetch = function (...fetchArgs) {
        // Changed from function () to function (...fetchArgs)
        const startTime = performance.now();
        const url = fetchArgs[1]; // Changed from restArgs[1] to fetchArgs[1]

        return originalFetch
          .apply(this, fetchArgs) // Changed from arguments to fetchArgs
          .then((response) => {
            const duration = performance.now() - startTime;
            const success = response.ok;

            sendNetworkData({
              duration: duration,
              method: fetchArgs[1]?.method || 'GET',
              status: response.status,
              success: success,
              type: 'fetch',
              url: typeof url === 'string' ? url : url.url,
            });

            return response;
          })
          .catch((error) => {
            const duration = performance.now() - startTime;

            sendNetworkData({
              duration: duration,
              error: error.message,
              method: fetchArgs[1]?.method || 'GET',
              success: false,
              type: 'fetch',
              url: typeof url === 'string' ? url : url.url,
            });

            throw error;
          });
      };
    }

    // Track XMLHttpRequest
    if (window.XMLHttpRequest) {
      const originalXHR = window.XMLHttpRequest.prototype.open;
      window.XMLHttpRequest.prototype.open = function (...xhrArgs) {
        // Changed from function () to function (...xhrArgs)
        const startTime = performance.now();
        const method = xhrArgs[0]; // Changed from arguments[0] to xhrArgs[0]
        const url = xhrArgs[1]; // Changed from restArgs[1] to xhrArgs[1]

        this.addEventListener('load', () => {
          const duration = performance.now() - startTime;
          const success = this.status >= 200 && this.status < 300;

          sendNetworkData({
            duration: duration,
            method: method,
            status: this.status,
            success: success,
            type: 'xhr',
            url: url,
          });
        });

        this.addEventListener('error', () => {
          const duration = performance.now() - startTime;

          sendNetworkData({
            duration: duration,
            error: 'Network error',
            method: method,
            success: false,
            type: 'xhr',
            url: url,
          });
        });

        this.addEventListener('timeout', () => {
          const duration = performance.now() - startTime;

          sendNetworkData({
            duration: duration,
            error: 'Timeout',
            method: method,
            success: false,
            type: 'xhr',
            url: url,
          });
        });

        return originalXHR.apply(this, xhrArgs); // Changed from args to xhrArgs
      };
    }
  }
}

/**
 * Send network request data to the background script
 */
function sendNetworkData(data) {
  chrome.runtime.sendMessage({
    data: {
      ...data,
      timestamp: Date.now(),
      url: window.location.href,
    },
    type: 'network',
  });
}

/**
 * Capture JavaScript errors and send them to the background script
 */
window.addEventListener('error', (errorEvent) => {
  const errorData = {
    colno: errorEvent.colno,
    filename: errorEvent.filename,
    lineno: errorEvent.lineno,
    message: errorEvent.message,
    stack: errorEvent.error ? errorEvent.error.stack : null,
  };

  chrome.runtime.sendMessage({
    error: errorData,
    type: 'js_error',
    url: window.location.href,
  });

  // Don't prevent the error from being reported in the console
  return false;
});

// Capture unhandled promise rejections
window.addEventListener('unhandledrejection', (event) => {
  const message = event.reason
    ? event.reason.message || event.reason.toString()
    : 'Unhandled Promise Rejection';

  const stack = event.reason?.stack ? event.reason.stack : null;

  chrome.runtime.sendMessage({
    error: {
      colno: 0,
      filename: window.location.href,
      lineno: 0,
      message: message,
      stack: stack,
    },
    type: 'js_error',
    url: window.location.href,
  });
});

// Main initialization
(() => {
  // Don't initialize in iframes
  if (window.self !== window.top) {
    return;
  }

  // Initialize monitoring
  initPerformanceMonitoring();
  initErrorTracking();
  initConsoleTracking();
  initNetworkTracking();

  // Notify that the content script is active
  console.log('Beauty CRM Neural MCP Connector is active on this page');
})();
