// DevTools Panel JavaScript
let requestsData = [];
let errorsData = [];
let port;

// Connect to the background script
function connectToBackground() {
  port = chrome.runtime.connect({ name: 'neural-mcp-devtools' });

  port.onMessage.addListener((message) => {
    if (message.type === 'requests') {
      requestsData = message.data;
      updateRequestsTable();
    } else if (message.type === 'errors') {
      errorsData = message.data;
      updateErrorsTable();
    }
  });

  port.onDisconnect.addListener(() => {
    console.log('Disconnected from background script');
    // Try to reconnect after a delay
    setTimeout(connectToBackground, 1000);
  });

  // Request initial data
  port.postMessage({ action: 'getRequests' });
  port.postMessage({ action: 'getErrors' });
}

// Initialize connection
connectToBackground();

// Tab switching functionality
for (const button of document.querySelectorAll('.tab-button')) {
  button.addEventListener('click', function () {
    // Remove active class from all tabs
    for (const btn of document.querySelectorAll('.tab-button')) {
      btn.classList.remove('active');
    }
    for (const content of document.querySelectorAll('.tab-content')) {
      content.classList.remove('active');
    }

    // Add active class to current tab
    this.classList.add('active');
    const tabId = this.getAttribute('data-tab');
    document.getElementById(tabId).classList.add('active');
  });
}

// Format timestamp
function formatTimestamp(timestamp) {
  const date = new Date(timestamp);
  return date.toLocaleTimeString();
}

// Format bytes
function formatBytes(bytes) {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return `${Number.parseFloat((bytes / k ** i).toFixed(2))} ${sizes[i]}`;
}

// Get status class
function getStatusClass(status) {
  if (status >= 200 && status < 300) return 'status-success';
  if (status >= 400 && status < 500) return 'status-warning';
  if (status >= 500) return 'status-error';
  return 'status-info';
}

// Update requests table
function updateRequestsTable() {
  const tableBody = document.querySelector('#requests-table tbody');
  const emptyState = document.getElementById('requests-empty');
  const filter = document.getElementById('request-filter').value.toLowerCase();

  if (requestsData.length === 0) {
    tableBody.innerHTML = '';
    emptyState.style.display = 'flex';
    return;
  }

  emptyState.style.display = 'none';
  tableBody.innerHTML = '';

  const filteredData = requestsData.filter(
    (req) =>
      req.url.toLowerCase().includes(filter) ||
      req.method.toLowerCase().includes(filter) ||
      req.contentType?.toLowerCase().includes(filter),
  );

  for (const req of filteredData) {
    const row = document.createElement('tr');

    // Status column with colored indicator
    const statusCell = document.createElement('td');
    const statusIndicator = document.createElement('span');
    statusIndicator.className = `status-indicator ${getStatusClass(req.status)}`;
    statusCell.appendChild(statusIndicator);
    statusCell.appendChild(document.createTextNode(req.status));
    row.appendChild(statusCell);

    // Method column
    const methodCell = document.createElement('td');
    methodCell.textContent = req.method;
    row.appendChild(methodCell);

    // URL column
    const urlCell = document.createElement('td');
    urlCell.textContent = req.url.split('?')[0]; // Remove query params for cleaner display
    urlCell.title = req.url; // Show full URL on hover
    row.appendChild(urlCell);

    // Time column
    const timeCell = document.createElement('td');
    timeCell.textContent = formatTimestamp(req.timestamp);
    row.appendChild(timeCell);

    // Size column
    const sizeCell = document.createElement('td');
    sizeCell.textContent = req.size ? formatBytes(req.size) : 'N/A';
    row.appendChild(sizeCell);

    // Type column
    const typeCell = document.createElement('td');
    typeCell.textContent = req.contentType || 'unknown';
    row.appendChild(typeCell);

    // Add row click handler to show details
    row.addEventListener('click', () => {
      console.log('Request details:', req);
      // Future enhancement: Show detailed view
    });

    tableBody.appendChild(row);
  }
}

// Update errors table
function updateErrorsTable() {
  const tableBody = document.querySelector('#errors-table tbody');
  const emptyState = document.getElementById('errors-empty');
  const filter = document.getElementById('error-filter').value.toLowerCase();

  if (errorsData.length === 0) {
    tableBody.innerHTML = '';
    emptyState.style.display = 'flex';
    return;
  }

  emptyState.style.display = 'none';
  tableBody.innerHTML = '';

  const filteredData = errorsData.filter(
    (err) =>
      err.message.toLowerCase().includes(filter) ||
      err.type.toLowerCase().includes(filter) ||
      err.source?.toLowerCase().includes(filter),
  );

  for (const err of filteredData) {
    const row = document.createElement('tr');

    // Timestamp column
    const timeCell = document.createElement('td');
    timeCell.textContent = formatTimestamp(err.timestamp);
    row.appendChild(timeCell);

    // Error type column
    const typeCell = document.createElement('td');
    typeCell.textContent = err.type;
    row.appendChild(typeCell);

    // Message column
    const messageCell = document.createElement('td');
    messageCell.textContent = err.message;
    messageCell.title = err.message; // Show full message on hover
    row.appendChild(messageCell);

    // Source column
    const sourceCell = document.createElement('td');
    sourceCell.textContent = err.source || 'unknown';
    row.appendChild(sourceCell);

    // Line column
    const lineCell = document.createElement('td');
    lineCell.textContent = err.line || 'N/A';
    row.appendChild(lineCell);

    // Add row click handler to show details
    row.addEventListener('click', () => {
      console.log('Error details:', err);
      // Future enhancement: Show stack trace
    });

    tableBody.appendChild(row);
  }
}

// Set up filter listeners
document
  .getElementById('request-filter')
  .addEventListener('input', updateRequestsTable);
document
  .getElementById('error-filter')
  .addEventListener('input', updateErrorsTable);

// Set up refresh button listeners
document.getElementById('refresh-requests').addEventListener('click', () => {
  port.postMessage({ action: 'getRequests' });
});

document.getElementById('refresh-errors').addEventListener('click', () => {
  port.postMessage({ action: 'getErrors' });
});

// Initial table updates
updateRequestsTable();
updateErrorsTable();
