{"author": "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "dependencies": {"@beauty-crm/platform-environment-names": "workspace:*", "@beauty-crm/platform-logger": "workspace:*", "@beauty-crm/platform-utilities": "workspace:*"}, "description": "Shared kernel for orchestrator and sagas for Beauty CRM", "devDependencies": {"rimraf": "^6.0.1", "typescript": "^5.8.3"}, "keywords": [], "license": "ISC", "main": "dist/index.js", "name": "@beauty-crm/product-kernel", "private": true, "scripts": {"build": "tsc", "clean": "<PERSON><PERSON><PERSON> dist", "dev": "tsc --watch", "format": "biome format --write --fix .", "lint": "biome lint .", "lint:check": "biome check .", "lint:fix": "biome lint --write .", "test": "echo 'No tests specified for product-kernel' && exit 0"}, "type": "module", "types": "dist/index.d.ts", "version": "1.0.0"}