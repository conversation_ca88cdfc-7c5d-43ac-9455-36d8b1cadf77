{"author": "", "description": "Shared types for appointments across planner and management systems.", "devDependencies": {"@beauty-crm/product-domain-types": "workspace:*", "rimraf": "^6.0.1", "typescript": "^5.8.3", "zod": "^3.23.8"}, "keywords": [], "license": "ISC", "main": "dist/index.js", "name": "@beauty-crm/product-appointment-types", "scripts": {"build": "tsc", "clean": "<PERSON><PERSON><PERSON> dist", "dev": "tsc --watch", "format": "biome format --write --fix .", "lint": "biome lint .", "lint:check": "biome check .", "lint:fix": "biome lint --write .", "test": "echo 'No tests specified for product-appointment-types' && exit 0"}, "types": "dist/index.d.ts", "version": "1.0.0"}