import type {
  BaseEntity,
  SalonId,
  StaffId,
  TreatmentId,
  TimeSlot,
} from '@beauty-crm/product-domain-types';
import type { DomainEvent } from '@beauty-crm/platform-eventing';

export type AppointmentStatus =
  | 'PENDING'
  | 'CONFIRMED'
  | 'IN_PROGRESS'
  | 'COMPLETED'
  | 'CANCELLED'
  | 'NO_SHOW';

export type AppointmentPriority = 'low' | 'medium' | 'high' | 'urgent';

export interface Appointment extends BaseEntity {
  salonId: SalonId;
  customerId: string;
  treatmentId: TreatmentId;
  staffId: StaffId;
  timeSlot: TimeSlot;
  status: AppointmentStatus;
  priority?: AppointmentPriority;
  notes?: string;
  cancellationReason?: string;
  metadata?: Record<string, unknown>;
}

export interface AppointmentRequest {
  customerId: string;
  treatmentId: TreatmentId;
  preferredStaffId?: StaffId;
  preferredTimeSlots: TimeSlot[];
  notes?: string;
}

export interface AppointmentRescheduleRequest {
  appointmentId: string;
  newTimeSlot: TimeSlot;
  reason?: string;
}

export interface AppointmentCancellationRequest {
  appointmentId: string;
  reason: string;
  requestRefund?: boolean;
}

export interface AppointmentReminder {
  appointmentId: string;
  type: 'email' | 'sms' | 'push';
  scheduledFor: Date;
  sent?: Date;
  status: 'pending' | 'sent' | 'failed';
  error?: string;
}

export interface AppointmentFeedback {
  appointmentId: string;
  rating: number;
  comment?: string;
  categories?: {
    serviceQuality?: number;
    staffProfessionalism?: number;
    timeliness?: number;
    cleanliness?: number;
    overall?: number;
  };
  createdAt: Date;
  updatedAt?: Date;
}

export interface AppointmentSettings {
  minAdvanceTime: number; // in minutes
  maxAdvanceTime: number; // in days
  allowReappointment: boolean;
  rescheduleTimeLimit: number; // in hours
  allowCancellation: boolean;
  cancellationTimeLimit: number; // in hours
  cancellationFee?: number;
  noShowFee?: number;
  reminders: {
    email?: {
      enabled: boolean;
      timing: number[]; // hours before appointment
    };
    sms?: {
      enabled: boolean;
      timing: number[]; // hours before appointment
    };
    push?: {
      enabled: boolean;
      timing: number[]; // hours before appointment
    };
  };
  bufferTime: {
    before: number; // in minutes
    after: number; // in minutes
  };
  overappointment: {
    allowed: boolean;
    maxOverAppointmentPerSlot?: number;
  };
}

export interface AppointmentEvent<TData = unknown> extends DomainEvent<TData> {}
