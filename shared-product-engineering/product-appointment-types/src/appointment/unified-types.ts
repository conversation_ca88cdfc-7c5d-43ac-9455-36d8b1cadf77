import { z } from 'zod';

// Local BaseEntity interface to avoid external dependencies
export interface BaseEntity {
  id: string;
  createdAt: Date;
  updatedAt: Date;
}

// Unified appointment status enum used across all systems
export enum UnifiedAppointmentStatus {
  PENDING = 'PENDING',
  CONFIRMED = 'CONFIRMED',
  SCHEDULED = 'SCHEDULED',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
  NO_SHOW = 'NO_SHOW',
  RESCHEDULED = 'RESCHEDULED',
}

// Unified appointment interface for cross-system compatibility
export interface UnifiedAppointment extends BaseEntity {
  // Core identifiers
  id: string;
  externalId?: string; // For cross-system mapping

  // Customer/Client data
  customerId: string;
  customerName: string;
  customerEmail: string;
  customerPhone?: string;

  // Salon/Staff data
  salonId: string;
  salonName?: string;
  staffId: string;
  staffName?: string;
  stationId?: string;

  // Service data
  treatmentId: string;
  treatmentName: string;
  treatmentDuration: number;
  treatmentPrice: number;

  // Scheduling
  startTime: Date;
  endTime: Date;
  timezone?: string;

  // Status & metadata
  status: UnifiedAppointmentStatus;
  source: 'PLANNER' | 'MANAGEMENT';
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Zod schema for UnifiedAppointmentStatus
export const UnifiedAppointmentStatusSchema = z.nativeEnum(
  UnifiedAppointmentStatus,
);

// Zod schema for UnifiedAppointment
export const UnifiedAppointmentSchema = z.object({
  // Core identifiers
  id: z.string(),
  externalId: z.string().optional(),

  // Customer/Client data
  customerId: z.string(),
  customerName: z.string(),
  customerEmail: z.string().email(),
  customerPhone: z.string().optional(),

  // Salon/Staff data
  salonId: z.string(),
  salonName: z.string().optional(),
  staffId: z.string(),
  staffName: z.string().optional(),
  stationId: z.string().optional(),

  // Service data
  treatmentId: z.string(),
  treatmentName: z.string(),
  treatmentDuration: z.number().positive(),
  treatmentPrice: z.number().positive(),

  // Scheduling
  startTime: z.date(),
  endTime: z.date(),
  timezone: z.string().optional(),

  // Status & metadata
  status: UnifiedAppointmentStatusSchema,
  source: z.enum(['PLANNER', 'MANAGEMENT']),
  notes: z.string().optional(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

// Type inference from schema
export type UnifiedAppointmentType = z.infer<typeof UnifiedAppointmentSchema>;
