{"dependencies": {"@beauty-crm/domain": "workspace:*", "@beauty-crm/infrastructure": "workspace:*", "@beauty-crm/product-domain-types": "workspace:*", "uuid": "^11.1.0"}, "devDependencies": {"@types/uuid": "^10.0.0", "rimraf": "^6.0.1", "typescript": "^5.8.3"}, "main": "dist/index.js", "name": "@beauty-crm/application", "private": true, "scripts": {"_prebuild": "computing-lifecycle generate tsconfig", "build": "tsc", "clean": "<PERSON><PERSON><PERSON> dist", "dev": "tsc --watch", "format": "biome format --write --fix .", "lint": "biome lint .", "lint:check": "biome check .", "lint:fix": "biome lint --write .", "test": "echo 'No tests specified for application' && exit 0"}, "types": "dist/index.d.ts", "version": "1.0.0"}