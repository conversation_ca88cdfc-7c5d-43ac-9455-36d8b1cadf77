{"dependencies": {"@beauty-crm/domain": "workspace:*", "@beauty-crm/platform-computing-lifecycle": "workspace:*", "@beauty-crm/platform-computing-runtime": "workspace:*", "@beauty-crm/platform-db-client": "workspace:*", "@beauty-crm/platform-environment-names": "workspace:*", "@beauty-crm/platform-logger": "workspace:*", "@beauty-crm/product-domain-types": "workspace:*", "@beauty-crm/product-responses": "workspace:*", "@prisma/client": "^6.5.0", "zod": "^3.24.2"}, "devDependencies": {"@types/node": "^22.13.13", "rimraf": "^6.0.1", "typescript": "^5.8.3"}, "main": "dist/index.js", "name": "@beauty-crm/infrastructure", "private": true, "scripts": {"_prebuild": "computing-lifecycle generate tsconfig", "build": "tsc", "clean": "<PERSON><PERSON><PERSON> dist", "dev": "tsc --watch", "format": "biome format --write --fix .", "lint": "biome lint .", "lint:check": "biome check .", "lint:fix": "biome lint --write .", "test": "echo 'No tests specified for infrastructure' && exit 0"}, "types": "dist/index.d.ts", "version": "1.0.0"}