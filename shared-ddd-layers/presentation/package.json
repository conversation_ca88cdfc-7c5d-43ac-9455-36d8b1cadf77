{"dependencies": {"@beauty-crm/application": "workspace:*", "@beauty-crm/domain": "workspace:*", "@beauty-crm/infrastructure": "workspace:*", "@beauty-crm/platform-computing-runtime": "workspace:*", "@beauty-crm/platform-utilities": "workspace:*", "@beauty-crm/product-domain-types": "workspace:*", "@beauty-crm/product-responses": "workspace:*"}, "devDependencies": {"rimraf": "^6.0.1", "typescript": "^5.8.3"}, "main": "dist/index.js", "name": "@beauty-crm/presentation", "scripts": {"_prebuild": "computing-lifecycle generate tsconfig", "build": "tsc", "clean": "<PERSON><PERSON><PERSON> dist", "dev": "tsc --watch", "format": "biome format --write --fix .", "lint": "biome lint .", "lint:check": "biome check .", "lint:fix": "biome lint --write .", "test": "echo 'No tests specified for presentation' && exit 0"}, "types": "dist/index.d.ts", "version": "1.0.0"}