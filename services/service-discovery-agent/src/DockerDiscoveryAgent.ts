import Docker from 'dockerode';
import {
  type ServiceConfig,
  type ServiceRegistration,
  ServiceRegistry,
} from './ServiceRegistry.js';

/**
 * Enhanced logging utility with timestamps and structured messages
 */
export const Logger = {
  debug(message: string, metadata?: Record<string, unknown>): void {
    const logEntry = metadata
      ? `[${Logger.timestamp()}] 🐛 ${message} ${JSON.stringify(metadata)}`
      : `[${Logger.timestamp()}] 🐛 ${message}`;
    console.debug(logEntry);
  },

  discovery(message: string, metadata?: Record<string, unknown>): void {
    const logEntry = metadata
      ? `[${Logger.timestamp()}] 🔍 ${message} ${JSON.stringify(metadata)}`
      : `[${Logger.timestamp()}] 🔍 ${message}`;
    console.log(logEntry);
  },

  error(message: string, error?: Error | Record<string, unknown>): void {
    const logEntry = error
      ? `[${Logger.timestamp()}] ❌ ${message} ${error instanceof Error ? error.message : JSON.stringify(error)}`
      : `[${Logger.timestamp()}] ❌ ${message}`;
    console.error(logEntry);
  },

  event(message: string, metadata?: Record<string, unknown>): void {
    const logEntry = metadata
      ? `[${Logger.timestamp()}] 📡 ${message} ${JSON.stringify(metadata)}`
      : `[${Logger.timestamp()}] 📡 ${message}`;
    console.log(logEntry);
  },

  info(message: string, metadata?: Record<string, unknown>): void {
    const logEntry = metadata
      ? `[${Logger.timestamp()}] i✨ ${message} ${JSON.stringify(metadata)}`
      : `[${Logger.timestamp()}] i✨ ${message}`;
    console.log(logEntry);
  },

  timestamp(): string {
    return new Date().toISOString();
  },

  warn(message: string, metadata?: Record<string, unknown>): void {
    const logEntry = metadata
      ? `[${Logger.timestamp()}] !⚠️ ${message} ${JSON.stringify(metadata)}`
      : `[${Logger.timestamp()}] !⚠️ ${message}`;
    console.warn(logEntry);
  },
};

export interface ContainerInfo {
  id: string;
  name: string;
  labels: Record<string, string>;
  state: string;
  networkSettings: Docker.NetworkSettings;
}

/**
 * Docker-based Service Discovery Agent
 * Monitors Docker containers and automatically registers/deregisters services
 */
export class DockerDiscoveryAgent {
  private docker: Docker;
  private serviceRegistry: ServiceRegistry;
  private registrations = new Map<string, ServiceRegistration>();
  private containerToServiceMap = new Map<string, string>(); // containerId -> serviceId
  private pollInterval: NodeJS.Timeout | null = null;
  private eventStream: NodeJS.ReadableStream | null = null;

  constructor(private natsUrl: string) {
    // Configure Docker to use the socket
    this.docker = new Docker({
      socketPath: '/var/run/docker.sock',
    });
    this.serviceRegistry = new ServiceRegistry(natsUrl);
  }

  /**
   * Start the discovery agent
   */
  async start(): Promise<void> {
    Logger.info('Starting Docker Discovery Agent...');

    try {
      // Connect to NATS first
      await this.serviceRegistry.connect();
      Logger.success('Connected to NATS', { url: this.natsUrl });

      // Initial discovery of existing containers
      await this.discoverServices();

      // Start polling for changes every 30 seconds
      this.pollInterval = setInterval(() => {
        this.discoverServices().catch((error) => {
          Logger.error('Error during periodic discovery:', error);
        });
      }, 30000);

      // Listen for real-time Docker events
      await this.listenForDockerEvents();

      // Publish service list every 5 minutes
      setInterval(
        () => {
          this.serviceRegistry.publishServiceList().catch((error) => {
            Logger.error('Error publishing service list:', error);
          });
        },
        5 * 60 * 1000,
      );

      Logger.success('Docker Discovery Agent started successfully');
    } catch (error) {
      Logger.error('Failed to start Docker Discovery Agent:', error as Error);
      throw error;
    }
  }

  /**
   * Discover all running Beauty CRM services
   */
  private async discoverServices(): Promise<void> {
    try {
      Logger.discovery('Discovering services...');

      const containers = await this.docker.listContainers({
        filters: {
          label: ['beauty-crm.service=true'],
          status: ['running'],
        },
      });

      Logger.info(`Found ${containers.length} Beauty CRM services`, {
        containerNames: containers.map((c) => c.Names[0]?.replace(/^\//, '')),
        count: containers.length,
      });

      for (const container of containers) {
        await this.registerService(container);
      }

      // Clean up registrations for containers that no longer exist
      await this.cleanupStaleRegistrations(containers);
    } catch (error) {
      Logger.error('Error discovering services:', error as Error);
    }
  }

  /**
   * Register a service from container information
   */
  private async registerService(
    container: Docker.ContainerInfo,
  ): Promise<void> {
    const labels = container.Labels || {};
    const serviceName = labels['beauty-crm.service.name'];
    const servicePort = Number.parseInt(
      labels['beauty-crm.service.port'] || '4000',
    );
    const healthEndpoint = labels['beauty-crm.service.health'] || '/health';
    const domain = labels['beauty-crm.service.domain'] || 'unknown';
    const version = labels['beauty-crm.service.version'] || '1.0.0';

    if (!serviceName) {
      Logger.warn(
        `Container ${container.Names[0]} missing service name label`,
        {
          containerId: container.Id.substring(0, 12),
          containerName: container.Names[0]?.replace(/^\//, ''),
          labels: Object.keys(labels),
        },
      );
      return;
    }

    const containerId = container.Id;

    // Check if this container is already registered
    const existingServiceId = this.containerToServiceMap.get(containerId);
    if (existingServiceId && this.registrations.has(existingServiceId)) {
      // Container already registered, just update last seen
      const existingRegistration = this.registrations.get(existingServiceId);
      if (existingRegistration) {
        existingRegistration.lastSeen = new Date().toISOString();
      }
      return;
    }

    // Generate a stable service ID based on container and service info
    const serviceId = `${serviceName}-${containerId.substring(0, 12)}`;

    // Get container name (remove leading slash)
    const containerName = container.Names[0].replace(/^\//, '');

    const serviceConfig: ServiceConfig = {
      capabilities: [
        domain,
        'backend',
        'api',
        'database',
        ...ServiceRegistry.detectCapabilities(),
      ],
      healthEndpoint,
      host: containerName,
      id: serviceId,
      metadata: {
        containerId: container.Id,
        containerName,
        discoveredAt: new Date().toISOString(),
        domain,
        environment: process.env.NODE_ENV || 'development',
        type: 'management',
      },
      name: serviceName,
      port: servicePort,
      version,
    };

    try {
      await this.serviceRegistry.registerService(serviceConfig);

      const registration: ServiceRegistration = {
        lastSeen: new Date().toISOString(),
        registeredAt: new Date().toISOString(),
        service: serviceConfig,
        status: 'unknown',
      };

      this.registrations.set(serviceId, registration);
      this.containerToServiceMap.set(containerId, serviceId);
      console.log(
        `✅ Registered service: ${serviceName} (${serviceId}) at ${containerName}:${servicePort}`,
      );
    } catch (error) {
      console.error(`❌ Failed to register ${serviceName}:`, error);
    }
  }

  /**
   * Clean up registrations for containers that no longer exist
   */
  private async cleanupStaleRegistrations(
    currentContainers: Docker.ContainerInfo[],
  ): Promise<void> {
    const currentContainerIds = new Set(currentContainers.map((c) => c.Id));

    // Check for containers that no longer exist
    for (const [
      containerId,
      serviceId,
    ] of this.containerToServiceMap.entries()) {
      if (!currentContainerIds.has(containerId)) {
        try {
          await this.serviceRegistry.deregisterService(serviceId);
          this.registrations.delete(serviceId);
          this.containerToServiceMap.delete(containerId);
          console.log(
            `🗑️ Cleaned up stale registration: ${serviceId} (container: ${containerId.substring(0, 12)})`,
          );
        } catch (error) {
          console.error(`❌ Failed to cleanup ${serviceId}:`, error);
        }
      }
    }
  }

  /**
   * Listen for real-time Docker events
   */
  private async listenForDockerEvents(): Promise<void> {
    try {
      this.eventStream = await this.docker.getEvents({
        filters: {
          event: ['start', 'stop', 'die', 'destroy'],
          type: ['container'],
        },
      });

      this.eventStream.on('data', async (chunk) => {
        try {
          const chunkStr = chunk.toString().trim();

          // Handle multiple JSON objects in a single chunk
          const lines = chunkStr
            .split('\n')
            .filter((line: string) => line.trim());

          for (const line of lines) {
            try {
              const event = JSON.parse(line) as {
                action: string;
                actor: {
                  id: string;
                  attributes: Record<string, string>;
                };
              };

              // Only process events for Beauty CRM services
              if (event.actor?.attributes?.['beauty-crm.service'] === 'true') {
                console.log(
                  `📡 Docker event: ${event.action} for ${event.actor.attributes['beauty-crm.service.name'] || 'unknown'}`,
                );

                if (event.action === 'start') {
                  // Wait a bit for the container to be fully ready
                  setTimeout(() => this.discoverServices(), 5000);
                } else if (['stop', 'die', 'destroy'].includes(event.action)) {
                  await this.deregisterService(event.actor.id);
                }
              }
            } catch (_parseError) {
              // Log individual line parsing errors but continue processing
              console.warn(
                `⚠️ Failed to parse Docker event line: ${line.substring(0, 100)}...`,
              );
            }
          }
        } catch (error) {
          console.error('❌ Error processing Docker event chunk:', error);
        }
      });

      this.eventStream.on('error', (error) => {
        console.error('❌ Docker event stream error:', error);
        // Try to reconnect after a delay
        setTimeout(() => this.listenForDockerEvents(), 10000);
      });
    } catch (error) {
      console.error('❌ Failed to listen for Docker events:', error);
    }
  }

  /**
   * Deregister a service by container ID
   */
  private async deregisterService(containerId: string): Promise<void> {
    const serviceId = this.containerToServiceMap.get(containerId);
    if (serviceId && this.registrations.has(serviceId)) {
      try {
        await this.serviceRegistry.deregisterService(serviceId);
        this.registrations.delete(serviceId);
        this.containerToServiceMap.delete(containerId);
        console.log(
          `🗑️ Deregistered service: ${serviceId} (container: ${containerId.substring(0, 12)})`,
        );
      } catch (error) {
        console.error(`❌ Failed to deregister ${serviceId}:`, error);
      }
    }
  }

  /**
   * Get current service registrations
   */
  getRegistrations(): Map<string, ServiceRegistration> {
    return new Map(this.registrations);
  }

  /**
   * Stop the discovery agent
   */
  async stop(): Promise<void> {
    console.log('🛑 Stopping Docker Discovery Agent...');

    // Stop polling
    if (this.pollInterval) {
      clearInterval(this.pollInterval);
      this.pollInterval = null;
    }

    // Close event stream
    if (this.eventStream) {
      if (
        'destroy' in this.eventStream &&
        typeof this.eventStream.destroy === 'function'
      ) {
        this.eventStream.destroy();
      }
      this.eventStream = null;
    }

    // Deregister all services
    const deregistrationPromises = Array.from(this.registrations.keys()).map(
      async (serviceId) => {
        try {
          await this.serviceRegistry.deregisterService(serviceId);
        } catch (error) {
          console.error('❌ Error during service deregistration:', error);
        }
      },
    );

    await Promise.all(deregistrationPromises);
    this.registrations.clear();
    this.containerToServiceMap.clear();

    // Disconnect from NATS
    await this.serviceRegistry.disconnect();

    console.log('✅ Docker Discovery Agent stopped');
  }
}
