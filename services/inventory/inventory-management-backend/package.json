{"dependencies": {"@beauty-crm/platform-environment-names": "workspace:*", "@beauty-crm/platform-identity-client": "workspace:*", "@beauty-crm/platform-logger": "workspace:*", "@beauty-crm/platform-utilities": "workspace:*", "@hono/node-server": "^1.13.8", "@prisma/client": "^6.5.0", "date-fns": "^4.1.0", "hono": "^4.7.2", "ioredis": "^5.5.0", "zod": "^3.24.2"}, "description": "API service managing inventory operations, stock levels, and product catalog for Beauty CRM", "devDependencies": {"@types/node": "^22.13.13", "prisma": "^6.5.0", "rimraf": "^6.0.1", "tsx": "^4.19.3", "typescript": "^5.8.3", "vite": "^6.2.0", "vitest": "^3.1.4"}, "name": "@beauty-crm/inventory-management-backend", "private": true, "scripts": {"build": "tsc", "clean": "<PERSON><PERSON><PERSON> dist", "dev": "tsx watch src/server.ts", "format": "biome format --write --fix .", "lint": "biome lint .", "lint:check": "biome check .", "lint:fix": "biome lint --write .", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "start": "computing-lifecycle start", "start:dev": "computing-lifecycle start", "start:prod": "computing-lifecycle start --app-env prod", "test": "vitest", "test:coverage": "vitest run --coverage"}, "type": "module", "version": "1.0.0"}