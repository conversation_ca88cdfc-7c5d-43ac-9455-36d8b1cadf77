{"dependencies": {"@beauty-crm/platform-computing-lifecycle": "workspace:*", "@tanstack/react-query": "^5.67.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router-dom": "^7.3.0", "zod": "^3.24.2"}, "devDependencies": {"@module-federation/vite": "^1.2.5", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "typescript": "^5.8.3", "vite": "^6.2.1"}, "name": "@beauty-crm/identity-sdk", "scripts": {"build": "tsc && vite build", "dev": "computing-lifecycle start --app-type frontend --app-name identity-sdk --app-env dev", "preview": "vite preview"}, "type": "module", "version": "1.0.0"}