{"author": "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "dependencies": {"@beauty-crm/platform-db-client": "workspace:*", "@beauty-crm/platform-logger": "workspace:*", "@beauty-crm/product-domain-types": "workspace:*", "@beauty-crm/product-identity-types": "workspace:*", "@hono/node-server": "^1.13.8", "@hono/zod-validator": "^0.4.3", "@opentelemetry/api": "^1.9.0", "@opentelemetry/auto-instrumentations-node": "^0.56.1", "@opentelemetry/exporter-trace-otlp-http": "^0.200.0", "@opentelemetry/instrumentation": "^0.57.2", "@opentelemetry/resources": "^2.0.0", "@opentelemetry/sdk-node": "^0.57.2", "@opentelemetry/sdk-trace-base": "^1.30.1", "@opentelemetry/semantic-conventions": "^1.30.0", "@paralleldrive/cuid2": "^2.2.2", "@prisma/client": "^6.5.0", "@types/fs-extra": "^11.0.4", "@upstash/redis": "^1.34.5", "argon2": "^0.41.1", "bcryptjs": "^3.0.2", "fs-extra": "^11.3.0", "hono": "^4.7.4", "jsonwebtoken": "^9.0.2", "node-fetch": "^3.3.2", "nodemailer": "^6.10.0", "otplib": "^12.0.1", "qrcode": "^1.5.4", "vite": "^6.2.2", "zod": "^3.24.2", "zxcvbn": "^4.4.2"}, "description": "Identity Management Service for Beauty CRM Tech Stack: Identity Management, Auth, MFA, User Management, etc. Tech Stack: Node.js, Bun, Prisma, Hono, Biome, Vitest, QRCode, Nodemailer, Jsonwebtoken, Otplib, Bcryptjs, Zod, Zxcvbn, QRCode, Nodemailer, Jsonwebtoken, Bcryptjs. ", "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^22.13.13", "@types/nodemailer": "^6.4.17", "@types/qrcode": "^1.5.5", "@types/zxcvbn": "^4.4.5", "prisma": "^6.5.0", "rimraf": "^6.0.1", "ts-node": "^10.9.2", "tsx": "^4.19.3", "typescript": "^5.8.3", "vitest": "^3.1.4"}, "keywords": [], "license": "ISC", "main": "dist/index.js", "name": "@beauty-crm/public-identity-management-backend", "resolutions": {"@biomejs/biome": "npm:@biomejs/wasm-web"}, "scripts": {"api:auth:test": "mdblaster src/tests/api/auth.blast.md", "api:auth:test:smart": "mdblaster src/tests/api/auth.smart.blast.md", "build": "tsc", "dev": "tsx watch src/index.ts", "format": "biome format --write --fix .", "lint": "biome lint .", "lint:check": "biome check .", "lint:fix": "biome lint --write .", "prisma:generate": "prisma generate", "prisma:migrate:dev": "prisma migrate dev", "prisma:migrate:prod": "prisma migrate deploy", "prisma:seed": "prisma db seed", "start": "ts-node src/index.ts", "test": "nx vite:test", "test:e2e": "nx vite:test --config vitest.config.e2e.ts", "verify-email": "node --loader ts-node/esm scripts/verify-email.ts"}, "types": "./dist/index.d.ts", "version": "2.0.0"}