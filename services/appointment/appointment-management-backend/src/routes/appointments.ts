import { Hono } from 'hono';
import type { AppointmentService } from '../application/services/AppointmentService';

export const appointmentRoutes = (appointmentService: AppointmentService) => {
  const router = new Hono();

  router.get('/', async (c) => {
    const appointments = await appointmentService.getAllAppointments();
    return c.json(appointments);
  });

  router.get('/:id', async (c) => {
    const appointment = await appointmentService.getAppointmentById(
      c.req.param('id'),
    );
    if (!appointment) {
      return c.json({ error: 'Appointment not found' }, 404);
    }
    return c.json(appointment);
  });

  router.post('/', async (c) => {
    const body = await c.req.json();
    try {
      const newAppointment = await appointmentService.createAppointment(body);
      return c.json(newAppointment, 201);
    } catch (error) {
      return c.json({ error: (error as Error).message }, 400);
    }
  });

  router.put('/:id', async (c) => {
    const body = await c.req.json();
    try {
      const updatedAppointment = await appointmentService.updateAppointment(
        c.req.param('id'),
        body,
      );
      return c.json(updatedAppointment);
    } catch (error) {
      return c.json({ error: (error as Error).message }, 404);
    }
  });

  router.delete('/:id', async (c) => {
    try {
      await appointmentService.deleteAppointment(c.req.param('id'));
      return c.json({ message: 'Appointment deleted successfully' });
    } catch (error) {
      return c.json({ error: (error as Error).message }, 404);
    }
  });

  return router;
};
