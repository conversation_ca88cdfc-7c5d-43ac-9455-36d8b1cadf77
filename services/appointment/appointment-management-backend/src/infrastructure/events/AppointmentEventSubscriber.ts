import {
  EventSubscriber,
  type DomainEvent,
} from '@beauty-crm/platform-eventing';
import type {
  AppointmentCreatedData,
  AppointmentUpdatedData,
  AppointmentCancelledData,
} from '@beauty-crm/platform-appointment-eventing';
import type { AppointmentService } from '../../application/services/AppointmentService';
import { Appointment } from '../../domain/models/AppointmentModel';

export class AppointmentEventSubscriber {
  private subscriber: EventSubscriber;

  constructor(private readonly appointmentService: AppointmentService) {
    this.subscriber = new EventSubscriber({
      serviceName: 'appointment-management-subscriber',
      natsUrl: process.env.NATS_URL || 'nats://localhost:4222',
      stream: 'APPOINTMENT_EVENTS',
    });

    this.subscriber.onError(async (error) => {
      console.error('NATS subscription error:', error);
    });
  }

  public async start(): Promise<void> {
    await this.subscriber.connect();
    console.log('Appointment event subscriber connected to NATS.');

    await this.subscriber.subscribe(
      'appointment.created',
      this.handleAppointmentCreated.bind(this),
    );
    await this.subscriber.subscribe(
      'appointment.updated',
      this.handleAppointmentUpdated.bind(this),
    );
    await this.subscriber.subscribe(
      'appointment.cancelled',
      this.handleAppointmentCancelled.bind(this),
    );
  }

  private async handleAppointmentCreated(
    event: DomainEvent<AppointmentCreatedData>,
  ): Promise<void> {
    console.log(`Received appointment.created event: ${event.eventId}`);
    try {
      const appointmentData = event.data.appointment;
      // Here you might have a transformer or directly map the data
      const newAppointment = new Appointment(appointmentData);
      await this.appointmentService.createFromExternal(newAppointment);
      console.log(`Successfully created appointment ${appointmentData.id}`);
    } catch (error) {
      console.error('Error processing appointment.created event:', error);
      // Implement dead-letter queue or other error handling strategy
    }
  }

  private async handleAppointmentUpdated(
    event: DomainEvent<AppointmentUpdatedData>,
  ): Promise<void> {
    console.log(`Received appointment.updated event: ${event.eventId}`);
    try {
      const appointmentData = event.data.appointment;
      await this.appointmentService.updateFromExternal(appointmentData);
      console.log(`Successfully updated appointment ${appointmentData.id}`);
    } catch (error) {
      console.error('Error processing appointment.updated event:', error);
    }
  }

  private async handleAppointmentCancelled(
    event: DomainEvent<AppointmentCancelledData>,
  ): Promise<void> {
    console.log(`Received appointment.cancelled event: ${event.eventId}`);
    try {
      const { appointmentId, reason } = event.data;
      await this.appointmentService.cancelFromExternal({
        id: appointmentId,
        cancellationReason: reason,
      });
      console.log(`Successfully cancelled appointment ${appointmentId}`);
    } catch (error) {
      console.error('Error processing appointment.cancelled event:', error);
    }
  }

  public async stop(): Promise<void> {
    await this.subscriber.disconnect();
  }
}
