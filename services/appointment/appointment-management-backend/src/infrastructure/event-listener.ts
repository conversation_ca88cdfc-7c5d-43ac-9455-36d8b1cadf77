import { PrismaClient } from '@prisma/client';
import { AppointmentService } from '../application/services/AppointmentService';
import { PrismaAppointmentRepository } from './repositories/PrismaAppointmentRepository';
import { AppointmentEventSubscriber } from './events/AppointmentEventSubscriber';

const prisma = new PrismaClient();
const appointmentRepository = new PrismaAppointmentRepository(prisma);
const appointmentService = new AppointmentService(appointmentRepository);
const appointmentEventSubscriber = new AppointmentEventSubscriber(
  appointmentService,
);

let shuttingDown = false;

async function main() {
  console.log('Starting event listener...');
  await appointmentEventSubscriber.start();
  console.log('Event listener started.');

  // Keep the process alive
  while (!shuttingDown) {
    await new Promise((resolve) => setTimeout(resolve, 1000));
  }

  console.log('Event listener shutting down.');
  await appointmentEventSubscriber.stop();
  await prisma.$disconnect();
}

function gracefulShutdown() {
  if (shuttingDown) return;
  shuttingDown = true;
  console.log('Received shutdown signal. Finishing current work...');
}

process.on('SIGTERM', gracefulShutdown);
process.on('SIGINT', gracefulShutdown);

main().catch((err) => {
  console.error('Event listener crashed:', err);
  process.exit(1);
});
