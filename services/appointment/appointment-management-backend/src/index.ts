import { serve } from '@hono/node-server';
import { Hono } from 'hono';
import { PrismaClient } from '@prisma/client';
import { AppointmentService } from './application/services/AppointmentService';
import { PrismaAppointmentRepository } from './infrastructure/repositories/PrismaAppointmentRepository';
import { appointmentRoutes } from './routes/appointments';

const app = new Hono();
const port = process.env.PORT ? Number.parseInt(process.env.PORT, 10) : 4001;

// Setup dependencies
const prisma = new PrismaClient();
const appointmentRepository = new PrismaAppointmentRepository(prisma);
const appointmentService = new AppointmentService(appointmentRepository);

// Setup routes
app.route('/appointments', appointmentRoutes(appointmentService));

app.get('/health', (c) => {
  return c.json({ status: 'ok' });
});

const server = serve({
  fetch: app.fetch,
  port,
});

console.log(`Management Backend running on http://localhost:${port}`);

process.on('SIGTERM', () => {
  console.log('SIGTERM signal received. Closing http server.');
  server.close(() => {
    console.log('Http server closed.');
    prisma.$disconnect();
  });
});
