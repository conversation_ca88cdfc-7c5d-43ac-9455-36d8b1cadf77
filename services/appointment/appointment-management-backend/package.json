{"dependencies": {"@beauty-crm/platform-appointment-eventing": "workspace:*", "@beauty-crm/platform-db-client": "workspace:*", "@beauty-crm/platform-eventing": "workspace:*", "@beauty-crm/platform-identity-client": "workspace:*", "@beauty-crm/platform-utilities": "workspace:*", "@beauty-crm/product-appointment-types": "workspace:*", "@hono/node-server": "^1.14.4", "@prisma/client": "^6.11.0", "date-fns": "^4.1.0", "hono": "^4.8.3", "ioredis": "^5.6.1", "nats": "^2.29.3", "node-schedule": "^2.1.1", "zod": "^3.25.67"}, "description": "API service managing appointment operations, business rules, and appointment workflows for Beauty CRM", "devDependencies": {"@types/node": "^22.15.34", "@types/node-schedule": "^2.1.7", "prisma": "^6.11.0", "rimraf": "^6.0.1", "tsx": "^4.20.3", "typescript": "^5.8.3", "vite": "^6.3.5", "vitest": "^3.2.4"}, "name": "@beauty-crm/appointment-management-backend", "private": true, "scripts": {"build": "bun build src/index.ts --outdir dist --target bun", "clean": "<PERSON><PERSON><PERSON> dist", "dev": "bun run --watch src/index.ts", "format": "biome format --write --fix .", "lint": "biome lint .", "lint:check": "biome check .", "lint:fix": "biome lint --write .", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:seed": "ts-node --compiler-options '{\"module\":\"CommonJS\"}' prisma/seed.ts", "start": "bun run src/index.ts", "start:prod": "bun run dist/index.js", "worker": "bun run src/infrastructure/event-listener.ts", "test": "vitest", "test:coverage": "vitest run --coverage", "tsc": "tsc"}, "type": "module", "version": "1.0.0"}