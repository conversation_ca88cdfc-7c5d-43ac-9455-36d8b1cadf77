import { Button } from '@beauty-crm/platform-introvertic-ui';
import {
  addWeeks,
  eachDayOfInterval,
  endOfWeek,
  format,
  parseISO,
  startOfWeek,
  subWeeks,
} from 'date-fns';
import { ChevronLeft, ChevronRight, Clock, User } from 'lucide-react';
import { useMemo, useState } from 'react';
import type { CalendarEvent, Staff } from './types';

interface GanttChartProps {
  events: CalendarEvent[];
  staff: Staff[];
  selectedStaff?: string | null;
  onStaffChange?: (staffId: string | null) => void;
}

// Helper function to safely convert event date to Date object
const getEventDate = (
  dateInput: string | Date | number | null | undefined,
): Date => {
  if (!dateInput) return new Date();
  if (typeof dateInput === 'string') return parseISO(dateInput);
  if (dateInput instanceof Date) return dateInput;
  if (typeof dateInput === 'number') return new Date(dateInput);
  return new Date();
};

export default function GanttChart({ events, staff }: GanttChartProps) {
  const [currentWeek, setCurrentWeek] = useState(new Date());

  // Calculate week range
  const weekStart = startOfWeek(currentWeek, { weekStartsOn: 1 }); // Monday
  const weekEnd = endOfWeek(currentWeek, { weekStartsOn: 1 }); // Sunday
  const weekDays = eachDayOfInterval({ end: weekEnd, start: weekStart });

  // Filter events for current week
  const weekEvents = useMemo(() => {
    return events.filter((event) => {
      if (!event.start) return false;
      const eventDate = getEventDate(event.start);
      return eventDate >= weekStart && eventDate <= weekEnd;
    });
  }, [events, weekStart, weekEnd]);

  // Group events by staff and day
  const staffSchedule = useMemo(() => {
    const schedule: Record<string, Record<string, typeof weekEvents>> = {};

    for (const staffMember of staff) {
      schedule[staffMember.id] = {};
      for (const day of weekDays) {
        const dayKey = format(day, 'yyyy-MM-dd');
        schedule[staffMember.id][dayKey] = weekEvents.filter((event) => {
          const eventStaffId = event.extendedProps?.appointment?.staffId;
          const eventDate = format(getEventDate(event.start), 'yyyy-MM-dd');
          return eventStaffId === staffMember.id && eventDate === dayKey;
        });
      }
    }

    return schedule;
  }, [staff, weekDays, weekEvents]);

  const navigateWeek = (direction: 'prev' | 'next') => {
    setCurrentWeek((prev) =>
      direction === 'prev' ? subWeeks(prev, 1) : addWeeks(prev, 1),
    );
  };

  const getEventBarStyle = (event: CalendarEvent) => {
    const startTime = getEventDate(event.start);
    const endTime = getEventDate(event.end);
    const startHour = startTime.getHours() + startTime.getMinutes() / 60;
    const endHour = endTime.getHours() + endTime.getMinutes() / 60;

    // Working hours: 9 AM to 6 PM (9 hours total)
    const workingHours = 9;
    const startOffset = Math.max(0, (startHour - 9) / workingHours) * 100;
    const width = Math.min(
      100 - startOffset,
      ((endHour - startHour) / workingHours) * 100,
    );

    return {
      backgroundColor: event.backgroundColor || '#4dabf7',
      borderColor: event.borderColor || '#339af0',
      left: `${startOffset}%`,
      width: `${width}%`,
    };
  };

  // const getStatusColor = (status: string) => {
  //   switch (status?.toLowerCase()) {
  //     case 'CONFIRMED':
  //       return 'bg-green-100 text-green-800 border-green-200';
  //     case 'SCHEDULED':
  //       return 'bg-blue-100 text-blue-800 border-blue-200';
  //     case 'completed':
  //       return 'bg-gray-100 text-gray-800 border-gray-200';
  //     case 'cancelled':
  //       return 'bg-red-100 text-red-800 border-red-200';
  //     default:
  //       return 'bg-blue-100 text-blue-800 border-blue-200';
  //   }
  // };

  return (
    <div className="p-6 bg-white rounded-lg shadow-sm">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">
            Staff Schedule - Gantt View
          </h2>
          <p className="text-gray-600">
            Week of {format(weekStart, 'MMM d')} -{' '}
            {format(weekEnd, 'MMM d, yyyy')}
          </p>
        </div>
        <div className="flex items-center gap-4">
          <Button variant="outline" onClick={() => navigateWeek('prev')}>
            <ChevronLeft className="w-4 h-4" />
            Previous Week
          </Button>
          <Button variant="outline" onClick={() => setCurrentWeek(new Date())}>
            Today
          </Button>
          <Button variant="outline" onClick={() => navigateWeek('next')}>
            Next Week
            <ChevronRight className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Time header */}
      <div className="grid grid-cols-8 gap-2 mb-4">
        <div className="p-3 text-sm font-medium text-gray-600">Staff</div>
        {weekDays.map((day) => (
          <div
            key={day.toISOString()}
            className="p-3 text-center border-b-2 border-gray-200"
          >
            <div className="text-sm font-medium text-gray-900">
              {format(day, 'EEE')}
            </div>
            <div className="text-xs text-gray-600">{format(day, 'MMM d')}</div>
          </div>
        ))}
      </div>

      {/* Staff rows */}
      <div className="space-y-3">
        {staff.map((staffMember) => (
          <div
            key={staffMember.id}
            className="grid grid-cols-8 gap-2 py-2 hover:bg-gray-50 rounded-lg"
          >
            {/* Staff info */}
            <div className="flex items-center p-3 border-r border-gray-200">
              <div className="flex items-center gap-2">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <User className="w-4 h-4 text-blue-600" />
                </div>
                <div>
                  <div className="text-sm font-medium text-gray-900">
                    {staffMember.name}
                  </div>
                  <div className="text-xs text-gray-600">
                    {staffMember.position}
                  </div>
                </div>
              </div>
            </div>

            {/* Daily schedule bars */}
            {weekDays.map((day) => {
              const dayKey = format(day, 'yyyy-MM-dd');
              const dayEvents = staffSchedule[staffMember.id]?.[dayKey] || [];

              return (
                <div
                  key={dayKey}
                  className="relative h-16 border border-gray-200 rounded bg-gray-50"
                >
                  {/* Time grid background */}
                  <div className="absolute inset-0 grid grid-cols-9 opacity-20">
                    {[
                      '09:00',
                      '10:00',
                      '11:00',
                      '12:00',
                      '13:00',
                      '14:00',
                      '15:00',
                      '16:00',
                      '17:00',
                    ].map((timeSlot) => (
                      <div
                        key={`time-slot-${timeSlot}`}
                        className="border-r border-gray-300 last:border-r-0"
                      />
                    ))}
                  </div>

                  {/* Events */}
                  {dayEvents.map((event, index) => {
                    const appointment = event.extendedProps?.appointment;
                    return (
                      <div
                        key={event.id}
                        className="absolute top-1 h-6 rounded px-1 text-xs text-white font-medium flex items-center gap-1 cursor-pointer hover:opacity-90 transition-opacity"
                        style={{
                          ...getEventBarStyle(event),
                          top: `${4 + index * 8}px`, // Stack multiple events
                          zIndex: 10 + index,
                        }}
                        title={`${
                          appointment?.treatmentName || 'treatmentName'
                        } - ${
                          appointment?.customerName || 'Customer'
                        } (${format(
                          getEventDate(event.start),
                          'h:mm a',
                        )} - ${format(getEventDate(event.end), 'h:mm a')})`}
                      >
                        <Clock className="w-3 h-3" />
                        <span className="truncate">
                          {appointment?.treatmentName || 'treatmentName'}
                        </span>
                      </div>
                    );
                  })}

                  {/* Day summary */}
                  {dayEvents.length > 0 && (
                    <div className="absolute bottom-1 right-1 text-xs text-gray-600 bg-white px-1 rounded">
                      {dayEvents.length} appt{dayEvents.length !== 1 ? 's' : ''}
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        ))}
      </div>

      {/* Legend */}
      <div className="mt-6 p-4 bg-gray-50 rounded-lg">
        <h3 className="text-sm font-medium text-gray-900 mb-3">Legend</h3>
        <div className="flex flex-wrap gap-4 text-xs">
          <div className="flex items-center gap-2">
            <div className="w-4 h-2 bg-blue-500 rounded" />
            <span className="text-gray-600">Scheduled</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-4 h-2 bg-green-500 rounded" />
            <span className="text-gray-600">Confirmed</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-4 h-2 bg-gray-500 rounded" />
            <span className="text-gray-600">Completed</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-4 h-2 bg-red-500 rounded" />
            <span className="text-gray-600">Cancelled</span>
          </div>
          <div className="text-gray-600">• Time scale: 9:00 AM - 6:00 PM</div>
        </div>
      </div>
    </div>
  );
}
