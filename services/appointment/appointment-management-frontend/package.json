{"dependencies": {"@beauty-crm/platform-introvertic-ui": "workspace:*", "@tanstack/react-query": "^5.81.2", "axios": "^1.10.0", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lightningcss-darwin-arm64": "^1.30.1", "lucide-react": "^0.513.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.2", "tailwind-merge": "^2.6.0", "zod": "^3.25.67"}, "description": "Mobile-first Appointment Management Frontend with enhanced responsive design", "devDependencies": {"@tailwindcss/vite": "^4.1.10", "@types/node": "^22.15.33", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "esbuild": "^0.25.5", "tailwindcss": "^4.1.10", "typescript": "^5.8.3", "vite": "^6.3.5", "vitest": "^3.2.4"}, "main": "index.js", "name": "@beauty-crm/appointment-management-frontend", "overrides": {"esbuild": "^0.25.5"}, "private": true, "resolutions": {"@biomejs/biome": "npm:@biomejs/wasm-web"}, "scripts": {"build": "vite build", "build:check": "npx tsc && vite build", "dev": "vite", "dev:direct": "bun exec vite", "dev:vite": "vite", "format": "biome format --write --fix .", "lint": "biome lint .", "lint:check": "biome check .", "lint:fix": "biome lint --write .", "preview": "vite preview", "test": "vitest", "test:all": "npx playwright test --reporter=list,json,html,blob > test-results/playwright-test-results.log 2>&1", "test:bdd": "cucumber-js tests/features --require tests/step-definitions/**/*.steps.ts --format progress --format json:test-results/cucumber-report.json", "test:bdd:e2e": "cucumber-js tests/e2e/features/**/*.feature --import 'tests/e2e/steps/**/*.ts' --import 'tests/e2e/support/**/*.ts' --format json:test-results/e2e-bdd-report.json --format html:test-results/e2e-bdd-report.html --tags '@e2e' --retry 2 --parallel 6 --fail-fast", "test:bdd:react": "cucumber-js --require 'tests/component/steps/**/*.ts'  --format json:test-results/react-bdd-report.json --format html:test-results/react-bdd-report.html tests/component/features/**/*.feature --tags '@react' --retry 1", "test:component": "vitest run tests/component/steps/appointment-creation.test.ts", "test:component:watch": "vitest watch tests/component/steps/appointment-creation.test.ts", "test:coverage": "vitest run --coverage", "test:e2e": "npx playwright test --reporter=list,json,html,blob > test-results/playwright-test-results.log 2>&1", "test:e2e:debug": "DEBUG=pw:* npx playwright test --debug --reporter=list,json,html,blob > test-results/playwright-debug-results.log 2>&1", "test:e2e:ui": "npx playwright test --ui --reporter=list,json,html,blob > test-results/playwright-ui-results.log 2>&1", "test:e2e:verbose": "DEBUG=pw:* npx playwright test --reporter=list,json,html,blob,line > test-results/playwright-verbose-results.log 2>&1", "test:schedule": "npx playwright test tests/e2e/stylist-schedule.spec.ts", "test:schedule:debug": "npx playwright test tests/e2e/stylist-schedule.spec.ts --debug", "test:schedule:ui": "npx playwright test tests/e2e/stylist-schedule.spec.ts --ui", "test:umux": "npx playwright test tests/e2e/appointment-umux.spec.ts", "test:umux:debug": "npx playwright test tests/e2e/appointment-umux.spec.ts --debug", "test:umux:ui": "npx playwright test tests/e2e/appointment-umux.spec.ts --ui"}, "type": "module", "version": "1.0.0"}