import {
  BaseAggregate,
  type Command,
  type DomainEvent,
} from '@beauty-crm/platform-eventing';
import {
  createAppointmentCancelledEvent,
  createAppointmentCreatedEvent,
  createAppointmentUpdatedEvent,
} from '@beauty-crm/platform-appointment-eventing';
import type { Appointment } from '@beauty-crm/product-appointment-types';
import type {
  CancelAppointmentCommand,
  CreateAppointmentCommand,
  UpdateAppointmentCommand,
} from '../commands/AppointmentCommands';

export class AppointmentAggregate extends BaseAggregate<Appointment> {
  protected getAggregateType(): string {
    return 'appointment';
  }

  protected getSource(): string {
    return 'appointment-planner-backend';
  }

  processCommand(command: Command): DomainEvent[] {
    this.validateCommand(command);

    switch (command.commandType) {
      case 'create-appointment':
        return this.handleCreateAppointment(
          command as CreateAppointmentCommand,
        );
      case 'update-appointment':
        return this.handleUpdateAppointment(
          command as UpdateAppointmentCommand,
        );
      case 'cancel-appointment':
        return this.handleCancelAppointment(
          command as CancelAppointmentCommand,
        );
      default:
        this.throwDomainError(`Unknown command type: ${command.commandType}`);
    }
  }

  private handleCreateAppointment(
    command: CreateAppointmentCommand,
  ): DomainEvent[] {
    this.validateCreateAppointment(command);

    const appointment: Appointment = {
      ...command.data,
      id: command.data.id || crypto.randomUUID(),
      status: 'PENDING',
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    this.state = appointment;

    const event = createAppointmentCreatedEvent(appointment, {
      source: this.getSource(),
      correlationId: command.metadata?.correlationId,
      userId: command.metadata?.userId,
    });

    return [event];
  }

  private handleUpdateAppointment(
    command: UpdateAppointmentCommand,
  ): DomainEvent[] {
    if (!this.state) {
      this.throwDomainError('Cannot update non-existent appointment');
    }

    this.validateUpdateAppointment(command);

    const updatedAppointment: Appointment = {
      ...this.state,
      ...command.data,
      updatedAt: new Date(),
    };

    this.state = updatedAppointment;

    const event = createAppointmentUpdatedEvent(
      updatedAppointment,
      command.data,
      {
        source: this.getSource(),
        correlationId: command.metadata?.correlationId,
        userId: command.metadata?.userId,
      },
    );

    return [event];
  }

  private handleCancelAppointment(
    command: CancelAppointmentCommand,
  ): DomainEvent[] {
    if (!this.state) {
      this.throwDomainError('Cannot cancel non-existent appointment');
    }

    const event = createAppointmentCancelledEvent(
      this.state.id,
      command.data.reason,
      {
        source: this.getSource(),
        correlationId: command.metadata?.correlationId,
        userId: command.metadata?.userId,
      },
    );

    // Optimistically update state
    this.state.status = 'CANCELLED';
    this.state.cancellationReason = command.data.reason;

    return [event];
  }

  private validateCommand(command: Command): void {
    if (!command.metadata?.correlationId) {
      this.throwDomainError('Correlation ID is required for all commands.');
    }
  }

  private validateCreateAppointment(command: CreateAppointmentCommand): void {
    const { data } = command;
    if (
      !data.salonId ||
      !data.customerId ||
      !data.treatmentId ||
      !data.timeSlot
    ) {
      this.throwDomainError(
        'Missing required fields for appointment creation.',
      );
    }
    if (new Date(data.timeSlot.startTime) >= new Date(data.timeSlot.endTime)) {
      this.throwDomainError('Start time must be before end time.');
    }
  }

  private validateUpdateAppointment(command: UpdateAppointmentCommand): void {
    if (command.data.id && command.data.id !== this.state?.id) {
      this.throwDomainError('Cannot change appointment ID');
    }
  }
}
