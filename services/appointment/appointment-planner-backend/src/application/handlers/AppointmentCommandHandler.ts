import {
  AggregateFactory,
  type CommandResult,
  createPrismaOutboxStorage,
  OutboxManager,
} from '@beauty-crm/platform-eventing';
import type { PrismaClient } from '@prisma/client';
import { AppointmentAggregate } from '../../domain/aggregates/AppointmentAggregate';
import {
  type CancelAppointmentCommand,
  type CreateAppointmentCommand,
  type UpdateAppointmentCommand,
  cancelAppointmentCommandSchema,
  createAppointmentCommandSchema,
  updateAppointmentCommandSchema,
} from '../../domain/commands/AppointmentCommands';
import type { AppointmentRepository } from '../../domain/ports/AppointmentRepository';

export class AppointmentCommandHandler {
  private outboxManager: OutboxManager;
  private aggregateFactory: AggregateFactory<AppointmentAggregate>;

  constructor(
    private readonly prisma: PrismaClient,
    private readonly appointmentRepository: AppointmentRepository,
  ) {
    const outboxStorage = createPrismaOutboxStorage(this.prisma);
    this.outboxManager = new OutboxManager(outboxStorage);
    this.aggregateFactory = new AggregateFactory(this.outboxManager);
  }

  async handleCreateAppointment(
    command: CreateAppointmentCommand,
  ): Promise<CommandResult> {
    const validationResult = createAppointmentCommandSchema.safeParse(command);
    if (!validationResult.success) {
      return {
        success: false,
        error: `Invalid command: ${validationResult.error.message}`,
      };
    }

    const aggregate = this.aggregateFactory.create(
      AppointmentAggregate,
      command.data.id,
    );
    const result = await aggregate.processCommandWithOutbox(command);

    if (result.success) {
      // Optionally, save a snapshot of the initial state
      await this.appointmentRepository.create(aggregate.state, this.prisma);
    }

    return result;
  }

  async handleUpdateAppointment(
    command: UpdateAppointmentCommand,
  ): Promise<CommandResult> {
    const validationResult = updateAppointmentCommandSchema.safeParse(command);
    if (!validationResult.success) {
      return {
        success: false,
        error: `Invalid command: ${validationResult.error.message}`,
      };
    }

    const existingState = await this.appointmentRepository.findById(
      command.aggregateId,
    );
    if (!existingState) {
      throw new Error(`Appointment ${command.aggregateId} not found`);
    }

    const aggregate = this.aggregateFactory.restore(
      AppointmentAggregate,
      command.aggregateId,
      existingState,
    );

    const result = await aggregate.processCommandWithOutbox(command);

    if (result.success) {
      await this.appointmentRepository.update(aggregate.state, this.prisma);
    }

    return result;
  }

  async handleCancelAppointment(
    command: CancelAppointmentCommand,
  ): Promise<CommandResult> {
    const validationResult = cancelAppointmentCommandSchema.safeParse(command);
    if (!validationResult.success) {
      return {
        success: false,
        error: `Invalid command: ${validationResult.error.message}`,
      };
    }

    const existingState = await this.appointmentRepository.findById(
      command.aggregateId,
    );
    if (!existingState) {
      throw new Error(`Appointment ${command.aggregateId} not found`);
    }

    const aggregate = this.aggregateFactory.restore(
      AppointmentAggregate,
      command.aggregateId,
      existingState,
    );

    const result = await aggregate.processCommandWithOutbox(command);

    if (result.success) {
      await this.appointmentRepository.update(aggregate.state, this.prisma);
    }

    return result;
  }
}
