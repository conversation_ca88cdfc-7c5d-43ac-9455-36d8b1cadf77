import {
  createPublisher,
  createPrismaOutboxStorage,
  OutboxManager,
} from '@beauty-crm/platform-eventing';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();
const publisher = createPublisher({
  natsUrl: process.env.NATS_URL || 'nats://localhost:4222',
  serviceName: 'appointment-planner-outbox-relayer',
  stream: {
    name: 'APPOINTMENT_EVENTS',
    subjects: ['appointment.events.*'],
  },
});

const outboxStorage = createPrismaOutboxStorage(prisma);
const outboxManager = new OutboxManager(outboxStorage);

let shuttingDown = false;

async function main() {
  console.log('Starting outbox relayer worker...');
  await publisher.connect();
  console.log('NATS publisher connected.');

  while (!shuttingDown) {
    try {
      const processedCount = await outboxManager.relay(publisher);
      if (processedCount > 0) {
        console.log(`Successfully relayed ${processedCount} events.`);
      }
    } catch (error) {
      console.error('Error relaying outbox events:', error);
    }
    // Wait for a bit before polling again
    await new Promise((resolve) => setTimeout(resolve, 5000));
  }

  console.log('Outbox relayer worker shutting down.');
  await publisher.disconnect();
  await prisma.$disconnect();
}

function gracefulShutdown() {
  if (shuttingDown) return;
  shuttingDown = true;
  console.log('Received shutdown signal. Finishing current batch...');
}

process.on('SIGTERM', gracefulShutdown);
process.on('SIGINT', gracefulShutdown);

main().catch((err) => {
  console.error('Worker crashed:', err);
  process.exit(1);
});
