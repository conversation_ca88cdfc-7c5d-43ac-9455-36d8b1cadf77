{"dependencies": {"@beauty-crm/platform-computing-lifecycle": "workspace:*", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-slot": "^1.1.2", "@tanstack/react-query": "^5.66.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "comlink": "^4.4.2", "lucide-react": "^0.487.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-router-dom": "^7.2.0", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.2"}, "description": "Identity Management Frontend for Beauty CRM", "devDependencies": {"@types/node": "^22.13.13", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "postcss": "^8.5.3", "tailwindcss": "^4.0.9", "typescript": "^5.8.3", "vite": "^6.2.0", "vite-plugin-top-level-await": "^1.5.0", "vite-plugin-wasm": "^3.4.1", "vitest": "^3.1.4"}, "name": "@beauty-crm/identity", "private": true, "scripts": {"build": "tsc && nx vite:build", "dev": "platform-shell-lifecycle start", "dev:vite": "nx serve", "format": "biome format --write --fix .", "lint": "biome lint .", "lint:check": "biome check .", "lint:fix": "biome lint --write .", "preview": "nx vite:preview", "test": "vitest", "test:coverage": "vitest run --coverage"}, "type": "module", "version": "1.0.0"}