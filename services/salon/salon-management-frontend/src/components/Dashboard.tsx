import {
  AlertCircle,
  ArrowLeft,
  Calendar,
  DollarSign,
  Loader2,
  UserPlus,
  Users,
} from 'lucide-react';
import type React from 'react';
import { Link, useParams } from 'react-router-dom';
import {
  useSalonAnalytics,
  useSalonStaff,
  useSalons,
  useSalonTreatments,
} from '../hooks/useSalonData';

interface Appointment {
  id: string;
  customerName: string;
  service: string;
  status: 'CONFIRMED' | 'PENDING' | 'CANCELLED';
  time: string;
}

interface StaffMember {
  id: string;
  firstName: string;
  lastName: string;
  role: string;
  isActive: boolean;
  specialties: string[];
}

const Dashboard: React.FC = () => {
  const { salonId } = useParams<{ salonId: string }>();
  const {
    data: salons,
    isLoading: salonsLoading,
    error: salonsError,
  } = useSalons();

  // Find the current salon by ID from URL params
  const currentSalon =
    salons?.find((salon: { id: string }) => salon.id === salonId) ||
    salons?.[0];
  const activeSalonId = salonId || currentSalon?.id || 'elegant-salon'; // fallback to mock data

  const { data: staff, isLoading: staffLoading } = useSalonStaff(activeSalonId);
  const { data: _treatments, isLoading: _treatmentsLoading } =
    useSalonTreatments(activeSalonId);
  const { data: analytics, isLoading: analyticsLoading } =
    useSalonAnalytics(activeSalonId);

  // Loading state
  if (salonsLoading || analyticsLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-indigo-600" />
        <span className="ml-2 text-gray-600">Loading dashboard...</span>
      </div>
    );
  }

  // Error state
  if (salonsError) {
    return (
      <div className="flex items-center justify-center h-64">
        <AlertCircle className="h-8 w-8 text-red-500" />
        <span className="ml-2 text-red-600">Failed to load dashboard data</span>
      </div>
    );
  }

  return (
    <div>
      {/* Page header */}
      <div className="mb-8">
        <div className="flex items-center mb-4">
          <Link
            to="/salons"
            className="flex items-center text-indigo-600 hover:text-indigo-800 transition-colors duration-200"
          >
            <ArrowLeft className="h-5 w-5 mr-2" />
            <span className="text-sm font-medium">Back to Salons</span>
          </Link>
        </div>
        <h1 className="text-2xl font-bold text-gray-900">
          {currentSalon?.name || 'Salon'} Dashboard
        </h1>
        <p className="mt-2 text-sm text-gray-700">
          Welcome to {currentSalon?.name || 'your salon'} management dashboard
        </p>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8">
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Calendar className="h-8 w-8 text-indigo-600" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Today's Appointments
                  </dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {analyticsLoading ? (
                      <Loader2 className="h-5 w-5 animate-spin" />
                    ) : (
                      analytics?.todayAppointments || 0
                    )}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Users className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Active Staff
                  </dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {staffLoading ? (
                      <Loader2 className="h-5 w-5 animate-spin" />
                    ) : (
                      staff?.filter((s: StaffMember) => s.isActive)?.length || 0
                    )}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <DollarSign className="h-8 w-8 text-yellow-600" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Today's Revenue
                  </dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {analyticsLoading ? (
                      <Loader2 className="h-5 w-5 animate-spin" />
                    ) : (
                      `${analytics?.todayRevenue?.toLocaleString() || 0}`
                    )}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <UserPlus className="h-8 w-8 text-purple-600" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    New Customers
                  </dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {analyticsLoading ? (
                      <Loader2 className="h-5 w-5 animate-spin" />
                    ) : (
                      analytics?.newCustomers || 0
                    )}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
              Recent Appointments
            </h3>
            {analyticsLoading ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-6 w-6 animate-spin text-indigo-600" />
                <span className="ml-2 text-gray-600">
                  Loading appointments...
                </span>
              </div>
            ) : (
              <div className="space-y-3">
                {analytics?.recentAppointments?.length > 0 ? (
                  analytics.recentAppointments.map(
                    (appointment: Appointment) => (
                      <div
                        key={appointment.id}
                        className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                      >
                        <div>
                          <p className="text-sm font-medium text-gray-900">
                            {appointment.customerName}
                          </p>
                          <p className="text-sm text-gray-500">
                            {appointment.service} - {appointment.time}
                          </p>
                        </div>
                        <span
                          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            appointment.status === 'CONFIRMED'
                              ? 'bg-green-100 text-green-800'
                              : appointment.status === 'PENDING'
                                ? 'bg-yellow-100 text-yellow-800'
                                : 'bg-gray-100 text-gray-800'
                          }`}
                        >
                          {appointment.status.charAt(0).toUpperCase() +
                            appointment.status.slice(1).toLowerCase()}
                        </span>
                      </div>
                    ),
                  )
                ) : (
                  <p className="text-sm text-gray-500 text-center py-4">
                    No recent appointments
                  </p>
                )}
              </div>
            )}
          </div>
        </div>

        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
              Staff Overview
            </h3>
            {staffLoading ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-6 w-6 animate-spin text-indigo-600" />
                <span className="ml-2 text-gray-600">Loading staff...</span>
              </div>
            ) : (
              <div className="space-y-3">
                {staff?.slice(0, 3).map((member: StaffMember) => (
                  <div
                    key={member.id}
                    className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                  >
                    <div>
                      <p className="text-sm font-medium text-gray-900">
                        {member.firstName} {member.lastName}
                      </p>
                      <p className="text-sm text-gray-500">{member.role}</p>
                    </div>
                    <div className="text-right">
                      <div
                        className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                          member.isActive
                            ? 'bg-green-100 text-green-800'
                            : 'bg-gray-100 text-gray-800'
                        }`}
                      >
                        {member.isActive ? 'Active' : 'Inactive'}
                      </div>
                      <p className="text-sm text-gray-500 mt-1">
                        {member.specialties?.slice(0, 2).join(', ') ||
                          'No specialties'}
                      </p>
                    </div>
                  </div>
                ))}
                {staff?.length > 0 ? null : (
                  <p className="text-sm text-gray-500 text-center py-4">
                    No staff members found
                  </p>
                )}
                {staff && staff.length > 3 && (
                  <div className="text-center pt-2">
                    <p className="text-sm text-indigo-600">
                      +{staff.length - 3} more staff members
                    </p>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
