import axios from 'axios';
import type { CreateSalonDto, UpdateSalonDto } from '../types/salon';

// API Configuration - Auto-detect routing method
const getApiBaseUrl = () => {
  // Check for environment variable first
  if (import.meta.env?.VITE_API_URL) {
    return import.meta.env.VITE_API_URL as string;
  }

  const currentHost = window.location.host;

  // Check if we're using API Gateway (beauty-crm.localhost)
  if (currentHost === 'beauty-crm.localhost') {
    return 'http://beauty-crm.localhost/api/salons';
  }

  // Default to domain-based routing
  return 'http://salon-management.localhost/api';
};

const API_BASE_URL = getApiBaseUrl();

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for auth
api.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  },
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    // Extract data from success response
    if (response.data?.success) {
      return { ...response, data: response.data.data };
    }
    return response;
  },
  (error) => {
    console.error('API Error:', error);
    return Promise.reject(error);
  },
);

export const salonApi = {
  create: async (data: CreateSalonDto) => {
    const response = await api.post('/', data);
    return response.data;
  },

  delete: async (id: string) => {
    const response = await api.delete(`/${id}`);
    return response.data;
  },
  getAll: async () => {
    try {
      const response = await api.get('');
      return response.data || [];
    } catch (error) {
      console.error('Failed to fetch salons:', error);
      // Return mock salon data until salon service is properly configured
      console.log('Salon service not configured - returning mock salon data');
      return [
        {
          address: '123 Main Street, Downtown',
          description: 'Premium beauty services in the heart of downtown',
          email: '<EMAIL>',
          id: 'elegant-salon',
          name: 'Elegant Beauty Salon',
          phone: '(*************',
          rating: 4.8,
          services: ['Hair', 'Nails', 'Skincare'],
          staff: 8,
          status: 'active',
        },
        {
          address: '456 Oak Avenue, Midtown',
          description: 'Contemporary hair and beauty treatments',
          email: '<EMAIL>',
          id: 'salon1',
          name: 'Modern Style Studio',
          phone: '(*************',
          rating: 4.6,
          services: ['Hair', 'Makeup', 'Styling'],
          staff: 6,
          status: 'active',
        },
        {
          address: '789 Pine Road, Uptown',
          description: 'Full-service spa and wellness center',
          email: '<EMAIL>',
          id: 'luxury-spa',
          name: 'Luxury Spa & Wellness',
          phone: '(*************',
          rating: 4.9,
          services: ['Spa', 'Massage', 'Skincare', 'Nails'],
          staff: 12,
          status: 'active',
        },
      ];
    }
  },

  getById: async (id: string) => {
    try {
      const response = await api.get(`/${id}`);
      return response.data;
    } catch (error) {
      console.error('Failed to fetch salon:', error);
      return null;
    }
  },

  update: async (id: string, data: UpdateSalonDto) => {
    const response = await api.put(`/${id}`, data);
    return response.data;
  },

  validate: async (salonId: string) => {
    const response = await api.get(`/${salonId}/validate`);
    return response.data;
  },
};

// Staff management - using staff service for staff data
export const staffApi = {
  getBySalon: async (salonId: string) => {
    try {
      // The staff service manages "staff"
      // For now, return mock staff data until staff service is implemented
      console.log(
        'Staff management not yet implemented - returning mock data for salon:',
        salonId,
      );
      return [
        {
          id: '1',
          name: 'Sarah Johnson',
          role: 'Senior Stylist',
          specialties: ['Hair Cutting', 'Coloring'],
          status: 'active',
        },
        {
          id: '2',
          name: 'Mike Chen',
          role: 'Nail Technician',
          specialties: ['Manicure', 'Pedicure'],
          status: 'active',
        },
      ];
    } catch (error) {
      console.error('Failed to fetch staff:', error);
      return [];
    }
  },
};

// Service/Treatment management is handled by the 'treatment' service (services/treatment)
export const serviceApi = {
  getBySalon: async (salonId: string) => {
    try {
      // Cross-service communication to treatment service
      const currentHost = window.location.host;
      const treatmentApiUrl =
        currentHost === 'beauty-crm.localhost'
          ? 'http://beauty-crm.localhost/api/treatments'
          : 'http://treatment-management.localhost/api';

      // Use the correct endpoint format for treatments
      const response = await axios.get(`${treatmentApiUrl}`, {
        params: { salonId },
      });

      // Handle the response format from the treatment service
      if (response.data?.success) {
        return response.data.data || [];
      }
      return response.data || [];
    } catch (error) {
      console.error('Failed to fetch services:', error);
      // Return mock service data until treatment service database is properly configured
      console.log(
        'Service management database not configured - returning mock data for salon:',
        salonId,
      );
      return [
        {
          category: 'Hair Services',
          description: 'Professional haircut with styling',
          duration: 60,
          id: '1',
          name: 'Hair Cut & Style',
          price: 45.0,
          status: 'active',
        },
        {
          category: 'Hair Services',
          description: 'Full hair coloring service',
          duration: 120,
          id: '2',
          name: 'Hair Color',
          price: 85.0,
          status: 'active',
        },
        {
          category: 'Nail Services',
          description: 'Classic manicure with polish',
          duration: 45,
          id: '3',
          name: 'Manicure',
          price: 25.0,
          status: 'active',
        },
      ];
    }
  },
};

export const treatmentApi = {
  getAll: async (salonId: string) => {
    try {
      // For cross-service communication, use the treatment service API
      const currentHost = window.location.host;
      const treatmentApiUrl =
        currentHost === 'beauty-crm.localhost'
          ? 'http://beauty-crm.localhost/api/treatments'
          : 'http://treatment-management.localhost/api';

      const response = await axios.get(`${treatmentApiUrl}`, {
        params: { salonId },
      });

      // Handle the response format from the treatment service
      if (response.data?.success) {
        return response.data.data || [];
      }
      return response.data || [];
    } catch (error) {
      console.error('Failed to fetch treatments:', error);
      // Return mock treatment data until treatment service database is properly configured
      console.log(
        'Treatment service database not configured - returning mock data for salon:',
        salonId,
      );
      return [
        {
          category: 'Hair Services',
          description: 'Professional haircut with styling',
          duration: 60,
          id: '1',
          name: 'Hair Cut & Style',
          price: 45.0,
          status: 'active',
        },
        {
          category: 'Hair Services',
          description: 'Full hair coloring service',
          duration: 120,
          id: '2',
          name: 'Hair Color',
          price: 85.0,
          status: 'active',
        },
        {
          category: 'Nail Services',
          description: 'Classic manicure with polish',
          duration: 45,
          id: '3',
          name: 'Manicure',
          price: 25.0,
          status: 'active',
        },
      ];
    }
  },
};

export default api;
