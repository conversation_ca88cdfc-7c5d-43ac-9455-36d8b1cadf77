{"browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"], "dependencies": {"@beauty-crm/platform-introvertic-ui": "workspace:*", "@tanstack/react-query": "^5.80.10", "axios": "^1.10.0", "clsx": "^2.1.1", "lucide-react": "^0.513.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.2", "tailwind-merge": "^2.6.0", "zod": "^3.25.67"}, "description": "Salon management frontend service for Beauty CRM", "devDependencies": {"@tailwindcss/vite": "^4.1.10", "@types/node": "^22.15.32", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.2", "esbuild": "^0.25.5", "rimraf": "^6.0.1", "tailwindcss": "^4.1.10", "typescript": "^5.8.3", "vite": "^6.3.5", "vitest": "^3.2.4"}, "name": "@beauty-crm/salon-management-frontend", "overrides": {"esbuild": "^0.25.5"}, "private": true, "resolutions": {"@biomejs/biome": "npm:@biomejs/wasm-web"}, "scripts": {"build": "npx vite build", "clean": "<PERSON><PERSON><PERSON> dist", "dev": "vite --host 0.0.0.0 --port 5173", "format": "biome format --write --fix .", "lint": "biome lint .", "lint:check": "biome check .", "lint:fix": "biome lint --write .", "start": "vite preview --host 0.0.0.0 --port 5173", "start:dev": "vite --host 0.0.0.0 --port 5173", "start:prod": "vite preview --host 0.0.0.0 --port 5173", "test": "vitest", "test:coverage": "vitest run --coverage"}, "type": "module", "version": "1.0.0"}