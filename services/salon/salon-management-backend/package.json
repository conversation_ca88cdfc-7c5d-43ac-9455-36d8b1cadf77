{"dependencies": {"@beauty-crm/platform-db-client": "workspace:*", "@beauty-crm/platform-eventing": "workspace:*", "@hono/node-server": "^1.14.3", "@paralleldrive/cuid2": "^2.2.2", "@prisma/client": "^6.9.0", "axios": "^1.7.9", "hono": "^4.7.11", "nats": "^2.29.3", "uuid": "^11.1.0", "zod": "^3.25.51"}, "devDependencies": {"@cucumber/cucumber": "^11.3.0", "@types/bun": "latest", "@types/chai": "^5.2.2", "@types/node": "^22.15.29", "@types/supertest": "^6.0.3", "@types/uuid": "^10.0.0", "chai": "^5.2.0", "dotenv": "^16.5.0", "esbuild": "^0.25.5", "prisma": "^6.9.0", "rimraf": "^6.0.1", "slugify": "^1.6.6", "supertest": "^7.1.1", "ts-node": "^10.9.2", "tsx": "^4.19.2", "typescript": "^5.8.3", "vitest": "^3.2.1"}, "name": "@beauty-crm/salon-management-backend", "peerDependencies": {"typescript": "^5"}, "resolutions": {"@biomejs/biome": "npm:@biomejs/wasm-web"}, "scripts": {"_prebuild": "computing-lifecycle generate tsconfig", "build": "tsc", "clean": "<PERSON><PERSON><PERSON> dist", "deploy:dev": "npm run deploy:dev -w @beauty-crm/platform-computing-lifecycle", "deploy:dev:iaac": "cdktf deploy --app \"npx ts-node ../../../shared-platform-engineering/shared-computing-lifecycle/src/platforms/vercel/main.ts\"", "deploy:prod": "npm run deploy:prod -w @beauty-crm/platform-computing-lifecycle", "deploy:stage": "npm run deploy:stage -w @beauty-crm/platform-computing-lifecycle", "dev": "bun src/index.ts", "format": "biome format --write --fix .", "lint": "biome lint .", "lint:check": "biome check .", "lint:fix": "biome lint --write .", "prisma:generate": "bunx prisma generate", "prisma:migrate": "bunx prisma migrate dev", "prisma:seed": "DATABASE_URL=postgresql://beauty_crm:beauty_crm_password@localhost:5432/beauty_crm_salon tsx prisma/seed.ts", "prisma:validate": "bunx prisma validate", "start": "bun src/index.ts", "start:dev": "bun src/index.ts", "start:prod": "bun src/index.ts", "start:stage": "bun src/index.ts", "test": "echo 'Not implemented'", "test:coverage": "vitest run --coverage", "test:cucumber": "NODE_OPTIONS=\"--loader ts-node/esm\" cucumber-js", "test:cucumber:advanced-database-manager": "DATABASE_URL=postgresql://postgres:postgres@localhost:5432/beauty_crm?schema=public NODE_OPTIONS=\"--loader ts-node/esm\" cucumber-js src/tests/features/advanced-database-manager/advanced-database-manager.feature --import src/tests/features/advanced-database-manager/advanced-database-manager.steps.ts", "test:cucumber:database-manager": "DATABASE_URL=postgresql://postgres:postgres@localhost:5432/beauty_crm?schema=public NODE_OPTIONS=\"--loader ts-node/esm\" cucumber-js src/tests/features/database-manager/database-manager.feature --import src/tests/features/database-manager/database-manager.steps.ts", "test:cucumber:list-salons": "NODE_OPTIONS=\"--loader ts-node/esm\" cucumber-js src/tests/features/list-salons/list-salons.feature --import src/tests/features/list-salons/list-salons.steps.ts", "test:watch": "vitest", "vercel:deploy": "npx vercel --prod --confirm --name salon-management-backend --json"}, "type": "module", "version": "1.0.0"}