{"author": "Beauty CRM Team", "dependencies": {"@beauty-crm/platform-db-client": "workspace:*", "@beauty-crm/platform-logger": "workspace:*", "@beauty-crm/product-domain-types": "workspace:*", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "http-proxy-middleware": "^2.0.6", "jsonwebtoken": "^9.0.2", "nats": "^2.28.2", "redis": "^4.6.12", "winston": "^3.11.0", "zod": "^3.25.48"}, "description": "Multi-tenant API Gateway for Beauty CRM SaaS", "devDependencies": {"@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^20.10.5", "@types/winston": "^2.4.4", "jest": "^29.7.0", "tsx": "^4.7.0", "typescript": "^5.3.3"}, "keywords": ["api-gateway", "multi-tenant", "saas", "beauty-crm", "microservices"], "license": "MIT", "main": "dist/index.js", "name": "@beauty-crm/api-gateway", "scripts": {"build": "tsc", "dev": "tsx watch src/index.ts", "format": "biome format --write --fix .", "lint": "biome lint .", "lint:check": "biome check .", "lint:fix": "biome lint --write .", "start": "node dist/index.js", "test": "jest"}, "version": "1.0.0"}