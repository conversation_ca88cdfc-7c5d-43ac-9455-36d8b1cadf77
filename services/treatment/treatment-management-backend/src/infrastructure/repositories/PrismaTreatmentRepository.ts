import type {
  PrismaClient,
  Treatment as PrismaTreatment,
} from '@prisma/client';
import { Treatment } from '../../domain/models/TreatmentModel';
import type { TreatmentRepository } from '../../domain/repositories/TreatmentRepository';

export class PrismaTreatmentRepository implements TreatmentRepository {
  constructor(private prisma: PrismaClient) {}

  private mapToTreatment(data: PrismaTreatment): Treatment {
    return new Treatment({
      basePrice: Number(data.basePrice),
      createdAt: data.createdAt,
      description: data.description || '',
      duration: data.duration,
      id: data.id,
      isActive: data.isActive,
      name: data.name,
      salonId: data.salonId,
      updatedAt: data.updatedAt,
    });
  }

  async create(treatment: Treatment): Promise<Treatment> {
    const data = await this.prisma.treatment.create({
      data: {
        basePrice: treatment.basePrice,
        description: treatment.description,
        duration: treatment.duration,
        isActive: treatment.isActive,
        name: treatment.name,
        salonId: treatment.salonId,
      },
    });

    return this.mapToTreatment(data);
  }

  async findById(id: string): Promise<Treatment | null> {
    const data = await this.prisma.treatment.findUnique({
      where: { id },
    });

    if (!data) return null;
    return this.mapToTreatment(data);
  }

  async findAll(): Promise<Treatment[]> {
    const data = await this.prisma.treatment.findMany({
      orderBy: { name: 'asc' },
    });

    return data.map((item) => this.mapToTreatment(item));
  }

  async findBySalonId(salonId: string): Promise<Treatment[]> {
    const data = await this.prisma.treatment.findMany({
      orderBy: { name: 'asc' },
      where: { salonId },
    });

    return data.map((item) => this.mapToTreatment(item));
  }

  async findActive(salonId: string): Promise<Treatment[]> {
    const data = await this.prisma.treatment.findMany({
      orderBy: { name: 'asc' },
      where: {
        isActive: true,
        salonId,
      },
    });

    return data.map(
      (item) =>
        new Treatment({
          basePrice: Number(item.basePrice),
          createdAt: item.createdAt,
          description: item.description || '',
          duration: item.duration,
          id: item.id,
          isActive: item.isActive,
          name: item.name,
          salonId: item.salonId,
          updatedAt: item.updatedAt,
        }),
    );
  }

  async update(treatment: Treatment): Promise<Treatment> {
    if (!treatment.id) {
      throw new Error('Treatment ID is required for update');
    }
    const data = await this.prisma.treatment.update({
      data: {
        basePrice: treatment.basePrice,
        description: treatment.description,
        duration: treatment.duration,
        isActive: treatment.isActive,
        name: treatment.name,
        updatedAt: treatment.updatedAt,
      },
      where: { id: treatment.id },
    });

    return new Treatment({
      basePrice: Number(data.basePrice),
      createdAt: data.createdAt,
      description: data.description || '',
      duration: data.duration,
      id: data.id,
      isActive: data.isActive,
      name: data.name,
      salonId: data.salonId,
      updatedAt: data.updatedAt,
    });
  }

  async delete(id: string): Promise<void> {
    await this.prisma.treatment.delete({
      where: { id },
    });
  }

  async search(salonId: string, query: string): Promise<Treatment[]> {
    const data = await this.prisma.treatment.findMany({
      orderBy: { name: 'asc' },
      where: {
        OR: [
          { name: { contains: query, mode: 'insensitive' } },
          { description: { contains: query, mode: 'insensitive' } },
        ],
        salonId,
      },
    });

    return data.map(
      (item) =>
        new Treatment({
          basePrice: Number(item.basePrice),
          createdAt: item.createdAt,
          description: item.description || '',
          duration: item.duration,
          id: item.id,
          isActive: item.isActive,
          name: item.name,
          salonId: item.salonId,
          updatedAt: item.updatedAt,
        }),
    );
  }

  async findByPriceRange(
    salonId: string,
    minPrice: number,
    maxPrice: number,
  ): Promise<Treatment[]> {
    const data = await this.prisma.treatment.findMany({
      orderBy: { basePrice: 'asc' },
      where: {
        basePrice: {
          gte: minPrice,
          lte: maxPrice,
        },
        salonId,
      },
    });

    return data.map(
      (item) =>
        new Treatment({
          basePrice: Number(item.basePrice),
          createdAt: item.createdAt,
          description: item.description || '',
          duration: item.duration,
          id: item.id,
          isActive: item.isActive,
          name: item.name,
          salonId: item.salonId,
          updatedAt: item.updatedAt,
        }),
    );
  }

  async findByDurationRange(
    salonId: string,
    minDuration: number,
    maxDuration: number,
  ): Promise<Treatment[]> {
    const data = await this.prisma.treatment.findMany({
      orderBy: { duration: 'asc' },
      where: {
        duration: {
          gte: minDuration,
          lte: maxDuration,
        },
        salonId,
      },
    });

    return data.map(
      (item) =>
        new Treatment({
          basePrice: Number(item.basePrice),
          createdAt: item.createdAt,
          description: item.description || '',
          duration: item.duration,
          id: item.id,
          isActive: item.isActive,
          name: item.name,
          salonId: item.salonId,
          updatedAt: item.updatedAt,
        }),
    );
  }

  async bulkToggleActive(
    salonId: string,
    treatmentIds: string[],
    isActive: boolean,
  ): Promise<number> {
    const result = await this.prisma.treatment.updateMany({
      data: {
        isActive,
        updatedAt: new Date(),
      },
      where: {
        id: {
          in: treatmentIds,
        },
        salonId,
      },
    });

    return result.count;
  }
}
