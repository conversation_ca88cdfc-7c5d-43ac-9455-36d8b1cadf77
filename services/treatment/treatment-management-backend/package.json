{"dependencies": {"@beauty-crm/platform-db-client": "workspace:*", "@prisma/client": "^6.9.0", "hono": "^4.7.11", "nats": "^2.29.3", "prisma": "^6.9.0", "zod": "^3.25.56"}, "description": "Treatment catalog management service", "devDependencies": {"@types/bun": "latest"}, "main": "src/index.ts", "module": "index.ts", "name": "@beauty-crm/treatment-management-backend", "peerDependencies": {"typescript": "^5"}, "private": true, "scripts": {"build": "bun build src/index.ts --outdir dist --target bun", "db:generate": "bunx prisma generate", "db:migrate": "bunx prisma migrate dev", "db:push": "bunx prisma db push", "db:studio": "bunx prisma studio", "dev": "bun run --watch src/index.ts", "start": "bun run src/index.ts"}, "type": "module", "version": "1.0.0"}