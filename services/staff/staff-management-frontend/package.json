{"dependencies": {"@beauty-crm/platform-introvertic-ui": "workspace:*", "@tanstack/react-query": "^5.80.7", "axios": "^1.9.0", "clsx": "^2.1.1", "lucide-react": "^0.513.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.2", "tailwind-merge": "^2.6.0", "zod": "^3.25.64"}, "description": "Staff management microfrontend for Beauty CRM", "devDependencies": {"@biomejs/biome": "^2.0.6", "@tailwindcss/vite": "^4.1.10", "@types/node": "^22.15.31", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.2", "esbuild": "^0.25.5", "tailwindcss": "^4.1.10", "typescript": "^5.8.3", "vite": "^6.3.5", "vitest": "^3.2.3"}, "name": "@beauty-crm/staff-management-frontend", "overrides": {"esbuild": "^0.25.5"}, "private": true, "scripts": {"build": "tsc && vite build", "dev": "vite", "format": "biome format --write --fix .", "lint": "biome lint .", "lint:check": "biome check .", "lint:fix": "biome lint --write .", "preview": "vite preview"}, "type": "module", "version": "0.0.0"}