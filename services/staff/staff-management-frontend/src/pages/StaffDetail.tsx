import { useQuery } from '@tanstack/react-query';
import { ArrowLeft, Clock, DollarSign, Edit, Tag, Trash2 } from 'lucide-react';
import type { ButtonHTMLAttributes, DetailedHTMLProps } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { staffApi } from '../utils/api';

// Simple components for now
const Card = ({
  children,
  className = '',
}: {
  children: React.ReactNode;
  className?: string;
}) => (
  <div className={`bg-white rounded-lg border shadow-sm ${className}`}>
    {children}
  </div>
);

const CardHeader = ({
  children,
  className = '',
}: {
  children: React.ReactNode;
  className?: string;
}) => <div className={`p-6 pb-3 ${className}`}>{children}</div>;

const CardTitle = ({
  children,
  className = '',
}: {
  children: React.ReactNode;
  className?: string;
}) => <h3 className={`text-lg font-semibold ${className}`}>{children}</h3>;

const CardContent = ({
  children,
  className = '',
}: {
  children: React.ReactNode;
  className?: string;
}) => <div className={`p-6 pt-0 ${className}`}>{children}</div>;

type ButtonProps = {
  children: React.ReactNode;
  className?: string;
  variant?: 'default' | 'destructive' | 'ghost' | 'outline';
} & DetailedHTMLProps<
  ButtonHTMLAttributes<HTMLButtonElement>,
  HTMLButtonElement
>;

const Button = ({
  children,
  className = '',
  variant = 'default',
  ...props
}: ButtonProps) => {
  const variants: Record<string, string> = {
    default: 'bg-blue-600 text-white hover:bg-blue-700',
    destructive: 'bg-red-600 text-white hover:bg-red-700',
    ghost: 'text-gray-700 hover:bg-gray-100',
    outline: 'border border-gray-300 text-gray-700 hover:bg-gray-50',
  };
  return (
    <button
      className={`px-4 py-2 rounded-md ${
        variants[variant] || variants.default
      } ${className}`}
      {...props}
      aria-label="Action button"
      type="button"
    >
      {children}
    </button>
  );
};

type BadgeProps = {
  children: React.ReactNode;
  variant?: 'default' | 'outline' | 'secondary';
  className?: string;
};

const Badge = ({
  children,
  variant = 'default',
  className = '',
}: BadgeProps) => {
  const variants: Record<string, string> = {
    default: 'bg-blue-100 text-blue-800',
    outline: 'border border-gray-300 text-gray-700',
    secondary: 'bg-gray-100 text-gray-800',
  };
  return (
    <span
      className={`px-2 py-1 text-xs rounded-full ${
        variants[variant] || variants.default
      } ${className}`}
    >
      {children}
    </span>
  );
};

export const StaffDetail = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  const {
    data: staff,
    isLoading,
    error,
  } = useQuery({
    enabled: !!id,
    queryFn: () => {
      if (!id) {
        return Promise.reject(new Error('No ID provided'));
      }
      return staffApi.getById(id);
    },
    queryKey: ['staff', id],
  });

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">Loading staff details...</div>
      </div>
    );
  }

  if (error || !staff) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg text-red-600">Staff not found</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" onClick={() => navigate('/staffs')}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Staffs
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{staff.name}</h1>
            <p className="text-muted-foreground">Staff Details</p>
          </div>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline">
            <Edit className="mr-2 h-4 w-4" />
            Edit
          </Button>
          <Button variant="destructive">
            <Trash2 className="mr-2 h-4 w-4" />
            Delete
          </Button>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Staff Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label
                htmlFor="staff-name"
                className="text-sm font-medium text-muted-foreground"
              >
                Name
              </label>
              <p id="staff-name" className="text-lg font-semibold">
                {staff.name}
              </p>
            </div>

            <div>
              <label
                htmlFor="staff-description"
                className="text-sm font-medium text-muted-foreground"
              >
                Description
              </label>
              <p id="staff-description" className="text-sm">
                {staff.description}
              </p>
            </div>

            <div>
              <label
                htmlFor="staff-status"
                className="text-sm font-medium text-muted-foreground"
              >
                Status
              </label>
              <div id="staff-status" className="mt-1">
                <Badge variant={staff.isActive ? 'default' : 'secondary'}>
                  {staff.isActive ? 'Active' : 'Inactive'}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Pricing & Duration</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-3">
              <Clock className="h-5 w-5 text-muted-foreground" />
              <div>
                <label
                  htmlFor="staff-duration"
                  className="text-sm font-medium text-muted-foreground"
                >
                  Duration
                </label>
                <p id="staff-duration" className="text-lg font-semibold">
                  {staff.duration} minutes
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <DollarSign className="h-5 w-5 text-muted-foreground" />
              <div>
                <label
                  htmlFor="staff-price"
                  className="text-sm font-medium text-muted-foreground"
                >
                  Price
                </label>
                <p id="staff-price" className="text-lg font-semibold">
                  €{staff.price}
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <Tag className="h-5 w-5 text-muted-foreground" />
              <div>
                <label
                  htmlFor="staff-category"
                  className="text-sm font-medium text-muted-foreground"
                >
                  Category
                </label>
                <div id="staff-category" className="mt-1">
                  <Badge variant="outline">
                    {staff.category.charAt(0).toUpperCase() +
                      staff.category.slice(1)}
                  </Badge>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Additional Details</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <label
                htmlFor="staff-id"
                className="text-sm font-medium text-muted-foreground"
              >
                Staff ID
              </label>
              <p id="staff-id" className="text-sm font-mono">
                {staff.id}
              </p>
            </div>

            <div>
              <label
                htmlFor="salon-id"
                className="text-sm font-medium text-muted-foreground"
              >
                Salon ID
              </label>
              <p id="salon-id" className="text-sm font-mono">
                {staff.salonId}
              </p>
            </div>

            <div>
              <label
                htmlFor="created-at"
                className="text-sm font-medium text-muted-foreground"
              >
                Created At
              </label>
              <p id="created-at" className="text-sm">
                {new Date(staff.createdAt).toLocaleDateString()}
              </p>
            </div>

            <div>
              <label
                htmlFor="updated-at"
                className="text-sm font-medium text-muted-foreground"
              >
                Last Updated
              </label>
              <p id="updated-at" className="text-sm">
                {new Date(staff.updatedAt).toLocaleDateString()}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
