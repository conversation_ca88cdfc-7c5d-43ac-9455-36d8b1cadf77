import axios from 'axios';

// API Configuration - Auto-detect routing method
const getApiBaseUrl = () => {
  // Check for environment variable first
  if (import.meta.env?.VITE_API_URL) {
    return import.meta.env.VITE_API_URL as string;
  }

  const currentHost = window.location.host;

  // Check if we're using API Gateway (beauty-crm.localhost)
  if (currentHost === 'beauty-crm.localhost') {
    return 'http://beauty-crm.localhost/api/staff';
  }

  // Default to domain-based routing
  return 'http://staff-management.localhost/api';
};

const API_BASE_URL = getApiBaseUrl();

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for auth
api.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  },
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    console.log('Response interceptor - raw response:', response);
    console.log('Response interceptor - response.data:', response.data);
    console.log(
      'Response interceptor - response.data?.success:',
      response.data?.success,
    );

    // Extract data from success response
    if (response.data?.success) {
      console.log(
        'Response interceptor - extracting data:',
        response.data.data,
      );
      return { ...response, data: response.data.data };
    }
    console.log('Response interceptor - returning original response');
    return response;
  },
  (error) => {
    console.error('API Error:', error);
    return Promise.reject(error);
  },
);

interface CreateStaffDto {
  firstName: string;
  lastName: string;
  notes?: string;
  isActive?: boolean;
}

export const staffApi = {
  create: async (data: CreateStaffDto) => {
    // Ensure salonId is included in the data
    const staffData = {
      salonId: 'salon1',
      ...data,
    };
    const response = await api.post('/', staffData);
    return response.data;
  },

  delete: async (id: string) => {
    const response = await api.delete(`/${id}`);
    return response.data;
  },
  getAll: async (salonId = 'salon1') => {
    try {
      const response = await api.get('', {
        params: { salonId },
      });
      console.log('Raw API response:', response);
      console.log('Response data:', response.data);
      console.log('Extracted staff data:', response.data);
      // Response interceptor already extracted the data array
      return response.data || [];
    } catch (error) {
      console.error('Failed to fetch staff:', error);
      return [];
    }
  },

  getById: async (id: string) => {
    try {
      const response = await api.get(`/${id}`);
      // Response interceptor already extracted the data
      return response.data || null;
    } catch (error) {
      console.error('Failed to fetch staff:', error);
      return null;
    }
  },

  getCategories: async () => {
    try {
      const response = await api.get('/categories');
      return response.data || [];
    } catch (error) {
      console.error('Failed to fetch categories:', error);
      return [];
    }
  },

  update: async (id: string, data: unknown) => {
    const response = await api.put(`/${id}`, data);
    return response.data;
  },
};

export const salonApi = {
  getAll: async () => {
    // For salon API calls, we need to use the salon API endpoint
    const response = await axios.get('http://beauty-crm.localhost/api/salons');

    return response;
  },

  validate: async (salonId: string) => {
    // For salon API calls, we need to use the salon API endpoint
    const response = await axios.get(
      `http://beauty-crm.localhost/api/salons/${salonId}/validate`,
    );
    return response;
  },
};

export default api;
