{"compilerOptions": {"allowJs": true, "baseUrl": ".", "esModuleInterop": true, "isolatedModules": true, "jsx": "react-jsx", "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "moduleResolution": "node", "noEmit": true, "paths": {"@/*": ["src/*"]}, "resolveJsonModule": true, "skipLibCheck": true, "strict": true, "target": "ES2020", "types": ["react", "react-dom", "node"]}, "exclude": ["node_modules", "dist", "**/node_modules", "**/dist"], "include": ["services/*/src/**/*", "shared-*/*/src/**/*", "scripts/**/*", "*.ts", "*.js"]}